{"info": {"_postman_id": "e1071cfa-736a-483e-af1e-851beb4b5afe", "name": "PPlusAnalytics", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "38663459", "_collection_link": "https://nnnn66.postman.co/workspace/Fliq~39afe66b-adb9-42a7-8a0f-4936505a0aa8/collection/38663459-e1071cfa-736a-483e-af1e-851beb4b5afe?action=share&source=collection_link&creator=38663459"}, "item": [{"name": "<PERSON><PERSON>", "item": [{"name": "login", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"password123\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/auth/login", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "login"]}}, "response": [{"name": "New Request", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"password123\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/auth/login", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "login"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "285"}, {"key": "ETag", "value": "W/\"11d-qLj2lbUiHp4jcBtEmPBKKBG5eWg\""}, {"key": "Date", "value": "<PERSON><PERSON>, 22 Jul 2025 14:10:43 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"id\": 1,\n        \"username\": \"admin\",\n        \"email\": \"<EMAIL>\",\n        \"role\": \"Company\",\n        \"status\": \"enabled\",\n        \"token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsImlhdCI6MTc1MzE5MzQ0MywiZXhwIjoxNzUzMjc5ODQzfQ.K3wOKRLP7qPsoPIPLlzBSn_bAKo6_KL-1odDotBfy7s\"\n    },\n    \"message\": \"\"\n}"}]}, {"name": "register", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"username\" : \"chumexxx\",\r\n    \"email\" : \"<EMAIL>\",\r\n    \"gender\" : \"male\",\r\n    \"password\": \"analytik1234\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/auth/register", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "register"]}}, "response": [{"name": "login Copy", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"username\" : \"chumexxx\",\r\n    \"email\" : \"<EMAIL>\",\r\n    \"gender\" : \"male\",\r\n    \"active\": \"true\",\r\n    \"role_id\": \"1\",\r\n    \"password\": \"analytik1234\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/auth/register", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "register"]}}, "status": "Unprocessable Entity", "code": 422, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "62"}, {"key": "ETag", "value": "W/\"3e-CK/p9wnqW82Vk2bbB2btkjRDZsU\""}, {"key": "Date", "value": "<PERSON><PERSON>, 22 Jul 2025 19:47:42 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": false,\n    \"message\": \"Email already exists\",\n    \"data\": null\n}"}]}]}, {"name": "Users", "item": [{"name": "getUsers", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/users/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", ""]}}, "response": [{"name": "New Request", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/users/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", ""]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "3556"}, {"key": "ETag", "value": "W/\"de4-iZGCIp5SvTmKnWZffUlDs61Hs/0\""}, {"key": "Date", "value": "<PERSON><PERSON>, 22 Jul 2025 22:22:06 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"data\": [\n            {\n                \"id\": 6,\n                \"username\": \"admin\",\n                \"email\": \"<EMAIL>\",\n                \"phone\": \"+2348012345678\",\n                \"gender\": \"male\",\n                \"active\": true,\n                \"expiration_date\": null,\n                \"role_id\": 1,\n                \"password\": \"$2a$10$yTlvivrK8Kle0johesgm/exryRAKo3JIuVpv9gM9YbruUIiRiEgRy\",\n                \"createdAt\": \"2025-07-22T13:11:19.925Z\",\n                \"updatedAt\": \"2025-07-22T13:11:19.925Z\",\n                \"role\": {\n                    \"id\": 1,\n                    \"name\": \"Company\"\n                }\n            },\n            {\n                \"id\": 1,\n                \"username\": \"admin\",\n                \"email\": \"<EMAIL>\",\n                \"phone\": \"+2348012345678\",\n                \"gender\": \"male\",\n                \"active\": true,\n                \"expiration_date\": null,\n                \"role_id\": 1,\n                \"password\": \"$2a$10$7jvhOlcePgyPmOPy4YNMt.msvLrFM3IHh9VLs7tU.8wwrABWgvqL.\",\n                \"createdAt\": \"2025-07-22T13:06:35.090Z\",\n                \"updatedAt\": \"2025-07-22T13:06:35.090Z\",\n                \"role\": {\n                    \"id\": 1,\n                    \"name\": \"Company\"\n                }\n            },\n            {\n                \"id\": 8,\n                \"username\": \"analyst1\",\n                \"email\": \"<EMAIL>\",\n                \"phone\": \"+2348012345680\",\n                \"gender\": \"male\",\n                \"active\": true,\n                \"expiration_date\": null,\n                \"role_id\": 3,\n                \"password\": \"$2a$10$yTlvivrK8Kle0johesgm/exryRAKo3JIuVpv9gM9YbruUIiRiEgRy\",\n                \"createdAt\": \"2025-07-22T13:11:19.925Z\",\n                \"updatedAt\": \"2025-07-22T13:11:19.925Z\",\n                \"role\": {\n                    \"id\": 3,\n                    \"name\": \"Analyst\"\n                }\n            },\n            {\n                \"id\": 3,\n                \"username\": \"analyst1\",\n                \"email\": \"<EMAIL>\",\n                \"phone\": \"+2348012345680\",\n                \"gender\": \"male\",\n                \"active\": true,\n                \"expiration_date\": null,\n                \"role_id\": 3,\n                \"password\": \"$2a$10$7jvhOlcePgyPmOPy4YNMt.msvLrFM3IHh9VLs7tU.8wwrABWgvqL.\",\n                \"createdAt\": \"2025-07-22T13:06:35.090Z\",\n                \"updatedAt\": \"2025-07-22T13:06:35.090Z\",\n                \"role\": {\n                    \"id\": 3,\n                    \"name\": \"Analyst\"\n                }\n            },\n            {\n                \"id\": 4,\n                \"username\": \"analyst2\",\n                \"email\": \"<EMAIL>\",\n                \"phone\": \"+2348012345681\",\n                \"gender\": \"female\",\n                \"active\": true,\n                \"expiration_date\": null,\n                \"role_id\": 3,\n                \"password\": \"$2a$10$7jvhOlcePgyPmOPy4YNMt.msvLrFM3IHh9VLs7tU.8wwrABWgvqL.\",\n                \"createdAt\": \"2025-07-22T13:06:35.090Z\",\n                \"updatedAt\": \"2025-07-22T13:06:35.090Z\",\n                \"role\": {\n                    \"id\": 3,\n                    \"name\": \"Analyst\"\n                }\n            },\n            {\n                \"id\": 9,\n                \"username\": \"analyst2\",\n                \"email\": \"<EMAIL>\",\n                \"phone\": \"+2348012345681\",\n                \"gender\": \"female\",\n                \"active\": true,\n                \"expiration_date\": null,\n                \"role_id\": 3,\n                \"password\": \"$2a$10$yTlvivrK8Kle0johesgm/exryRAKo3JIuVpv9gM9YbruUIiRiEgRy\",\n                \"createdAt\": \"2025-07-22T13:11:19.925Z\",\n                \"updatedAt\": \"2025-07-22T13:11:19.925Z\",\n                \"role\": {\n                    \"id\": 3,\n                    \"name\": \"Analyst\"\n                }\n            },\n            {\n                \"id\": 11,\n                \"username\": \"chumexxx\",\n                \"email\": \"<EMAIL>\",\n                \"phone\": null,\n                \"gender\": \"female\",\n                \"active\": true,\n                \"expiration_date\": null,\n                \"role_id\": 1,\n                \"password\": \"$2a$10$jQY8cR70w7Zca4Yj5lOhU.BfVTHj8Wre16yoEdX/.5AQ/qjslJPxi\",\n                \"createdAt\": \"2025-07-22T19:46:58.930Z\",\n                \"updatedAt\": \"2025-07-22T21:51:44.830Z\",\n                \"role\": {\n                    \"id\": 1,\n                    \"name\": \"Company\"\n                }\n            },\n            {\n                \"id\": 10,\n                \"username\": \"client1\",\n                \"email\": \"<EMAIL>\",\n                \"phone\": \"+2348012345682\",\n                \"gender\": \"male\",\n                \"active\": true,\n                \"expiration_date\": \"2026-07-22T13:11:19.925Z\",\n                \"role_id\": 4,\n                \"password\": \"$2a$10$yTlvivrK8Kle0johesgm/exryRAKo3JIuVpv9gM9YbruUIiRiEgRy\",\n                \"createdAt\": \"2025-07-22T13:11:19.925Z\",\n                \"updatedAt\": \"2025-07-22T13:11:19.925Z\",\n                \"role\": {\n                    \"id\": 4,\n                    \"name\": \"Client\"\n                }\n            },\n            {\n                \"id\": 5,\n                \"username\": \"client1\",\n                \"email\": \"<EMAIL>\",\n                \"phone\": \"+2348012345682\",\n                \"gender\": \"male\",\n                \"active\": true,\n                \"expiration_date\": \"2026-07-22T13:06:35.091Z\",\n                \"role_id\": 4,\n                \"password\": \"$2a$10$7jvhOlcePgyPmOPy4YNMt.msvLrFM3IHh9VLs7tU.8wwrABWgvqL.\",\n                \"createdAt\": \"2025-07-22T13:06:35.091Z\",\n                \"updatedAt\": \"2025-07-22T13:06:35.091Z\",\n                \"role\": {\n                    \"id\": 4,\n                    \"name\": \"Client\"\n                }\n            },\n            {\n                \"id\": 2,\n                \"username\": \"supervisor\",\n                \"email\": \"<EMAIL>\",\n                \"phone\": \"+2348012345679\",\n                \"gender\": \"female\",\n                \"active\": true,\n                \"expiration_date\": null,\n                \"role_id\": 2,\n                \"password\": \"$2a$10$7jvhOlcePgyPmOPy4YNMt.msvLrFM3IHh9VLs7tU.8wwrABWgvqL.\",\n                \"createdAt\": \"2025-07-22T13:06:35.090Z\",\n                \"updatedAt\": \"2025-07-22T13:06:35.090Z\",\n                \"role\": {\n                    \"id\": 2,\n                    \"name\": \"Supervisor\"\n                }\n            }\n        ],\n        \"pagination\": {\n            \"total\": 11,\n            \"page\": 1,\n            \"limit\": 10,\n            \"totalPages\": 2\n        }\n    },\n    \"message\": \"Users retrieved successfully\"\n}"}]}, {"name": "getSupervisors", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/users/supervisors", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "supervisors"]}}, "response": [{"name": "getUsers Copy", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/users/supervisors", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "supervisors"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "73"}, {"key": "ETag", "value": "W/\"49-Inx0jbeN3UgdtX86APUKGHuYFj0\""}, {"key": "Date", "value": "<PERSON><PERSON>, 22 Jul 2025 21:25:19 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": [],\n    \"message\": \"Supervisors retrieved successfully\"\n}"}]}, {"name": "getById", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/users/11", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "11"]}}, "response": [{"name": "getSupervisors Copy", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/users/11", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "11"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "197"}, {"key": "ETag", "value": "W/\"c5-m6nxq0fZDXHmivyRxHeo5CD42R8\""}, {"key": "Date", "value": "<PERSON><PERSON>, 22 Jul 2025 22:03:44 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"id\": 11,\n        \"username\": \"chumexxx\",\n        \"email\": \"<EMAIL>\",\n        \"gender\": \"female\",\n        \"status\": \"enabled\",\n        \"role\": \"Company\",\n        \"date\": \"2025-07-22T19:46:58.930Z\"\n    },\n    \"message\": \"User fetched\"\n}"}]}, {"name": "updateUser", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"gender\": \"female\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/users/update/11", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "update", "11"]}}, "response": [{"name": "getById Copy", "originalRequest": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"gender\": \"female\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/users/update/11", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "update", "11"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "50"}, {"key": "ETag", "value": "W/\"32-7k3Ls27uNV6C346HHHf7Lde41cI\""}, {"key": "Date", "value": "<PERSON><PERSON>, 22 Jul 2025 22:55:21 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {},\n    \"message\": \"User Update\"\n}"}]}, {"name": "deleteUser", "request": {"method": "PUT", "header": [], "url": {"raw": "http://localhost:3000/api/users/delete/11", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "delete", "11"]}}, "response": [{"name": "updateUser Copy", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "http://localhost:3000/api/users/delete/11", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "delete", "11"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "61"}, {"key": "ETag", "value": "W/\"3d-cnKgoPnrGKDmQFu20T8napddGIg\""}, {"key": "Date", "value": "<PERSON><PERSON>, 22 Jul 2025 22:55:34 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": false,\n    \"message\": \"User does not exist\",\n    \"data\": null\n}"}]}]}, {"name": "Activities", "item": [{"name": "GetAllActivities", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/audit-logs/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "audit-logs", ""]}}, "response": [{"name": "New Request", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/audit-logs/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "audit-logs", ""]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "315"}, {"key": "ETag", "value": "W/\"13b-wCvJbLZURwyeIUaMK7layvDtYGc\""}, {"key": "Date", "value": "<PERSON><PERSON>, 22 Jul 2025 22:29:57 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"activity\": [\n            {\n                \"id\": 2,\n                \"name\": \"Money\"\n            }\n        ],\n        \"meta\": {\n            \"total\": 1,\n            \"currentPage\": 1,\n            \"totalPage\": 1,\n            \"pageSize\": 10\n        },\n        \"links\": {\n            \"first\": \"undefined/activitiess?page=1\",\n            \"last\": \"undefined/activitiess?page=1\",\n            \"prev\": \"undefined/activitiess?page=1\",\n            \"next\": \"undefined/activitiess?pages=1\"\n        }\n    },\n    \"message\": \"All Activitys\"\n}"}]}, {"name": "getActivity", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/activities/1", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "activities", "1"]}}, "response": [{"name": "getActivites Copy", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/activities/1", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "activities", "1"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "60"}, {"key": "ETag", "value": "W/\"3c-uKzn05iJvI+13yWiN7sjgxDDoZY\""}, {"key": "Date", "value": "<PERSON><PERSON>, 22 Jul 2025 22:56:15 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": false,\n    \"message\": \"Activity Not found\",\n    \"data\": null\n}"}]}, {"name": "createActivity", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"Money\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/activities/create", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "activities", "create"]}}, "response": [{"name": "getActivites Copy 2", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"Money\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/activities/create", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "activities", "create"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "55"}, {"key": "ETag", "value": "W/\"37-CZ0qd/0VIgLGhQYPuzQQbk2CNLE\""}, {"key": "Date", "value": "<PERSON><PERSON>, 22 Jul 2025 22:29:52 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {},\n    \"message\": \"Activity Created\"\n}"}]}, {"name": "updateActivity", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\" : \"<PERSON><PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/activities/update/1", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "activities", "update", "1"]}}, "response": [{"name": "createActivity Copy", "originalRequest": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\" : \"<PERSON><PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/activities/update/1", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "activities", "update", "1"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "55"}, {"key": "ETag", "value": "W/\"37-Lo/op3hCGhsgDiV9UvhgZeOrtu0\""}, {"key": "Date", "value": "<PERSON><PERSON>, 22 Jul 2025 22:28:32 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {},\n    \"message\": \"Activity Updated\"\n}"}]}, {"name": "deleteActivity", "request": {"method": "PUT", "header": [], "url": {"raw": "http://localhost:3000/api/activities/delete/1", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "activities", "delete", "1"]}}, "response": [{"name": "updateActivity Copy", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "http://localhost:3000/api/activities/delete/1", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "activities", "delete", "1"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "55"}, {"key": "ETag", "value": "W/\"37-nX48XbqWihamv/Dkk6m7mzWB3Kc\""}, {"key": "Date", "value": "<PERSON><PERSON>, 22 Jul 2025 22:29:37 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {},\n    \"message\": \"Activity Deleted\"\n}"}]}]}, {"name": "Analytics", "item": [{"name": "getDashboardSummary", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/analytics/dashboard-summary", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "analytics", "dashboard-summary"]}}, "response": [{"name": "New Request", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/analytics/dashboard-summary", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "analytics", "dashboard-summary"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "3534"}, {"key": "ETag", "value": "W/\"dce-RYzCVLAVWqDG0GcBbGWpTrsoAtg\""}, {"key": "Date", "value": "<PERSON><PERSON>, 22 Jul 2025 22:42:28 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"totals\": {\n            \"mentions\": 8,\n            \"swotAnalyses\": 0,\n            \"outcomeInsights\": 0,\n            \"companies\": 10,\n            \"users\": 11\n        },\n        \"sentiment\": [\n            {\n                \"sentiment\": \"positive\",\n                \"count\": \"6\"\n            },\n            {\n                \"sentiment\": \"negative\",\n                \"count\": \"2\"\n            }\n        ],\n        \"recentActivity\": [\n            {\n                \"id\": 8,\n                \"company_id\": 3,\n                \"publication_id\": 3,\n                \"date\": \"2024-01-22T00:00:00.000Z\",\n                \"headline\": \"GTBank Wins Best Digital Bank Award\",\n                \"content\": \"Guaranty Trust Bank has been recognized as the Best Digital Bank in Nigeria at the annual Banking Excellence Awards for its innovative digital solutions.\",\n                \"sentiment\": \"positive\",\n                \"sentiment_score\": \"0.90\",\n                \"reach\": \"450000\",\n                \"engagement\": 1100,\n                \"media_type\": \"print\",\n                \"url\": \"https://thisdaylive.com/gtbank-digital-award\",\n                \"analyst_id\": 4,\n                \"tags\": [\n                    \"award\",\n                    \"digital banking\",\n                    \"recognition\"\n                ],\n                \"status\": \"published\",\n                \"createdAt\": \"2025-07-22T13:11:19.988Z\",\n                \"updatedAt\": \"2025-07-22T13:11:19.988Z\",\n                \"company\": {\n                    \"name\": \"Guaranty Trust Bank\"\n                }\n            },\n            {\n                \"id\": 2,\n                \"company_id\": 2,\n                \"publication_id\": 4,\n                \"date\": \"2024-01-16T00:00:00.000Z\",\n                \"headline\": \"MTN Nigeria Launches 5G Network in Lagos\",\n                \"content\": \"MTN Nigeria has officially launched its 5G network services in Lagos, marking a significant milestone in the country's telecommunications advancement.\",\n                \"sentiment\": \"positive\",\n                \"sentiment_score\": \"0.90\",\n                \"reach\": \"2000000\",\n                \"engagement\": 5000,\n                \"media_type\": \"broadcast\",\n                \"url\": \"https://channelstv.com/mtn-5g-launch\",\n                \"analyst_id\": 4,\n                \"tags\": [\n                    \"5G\",\n                    \"technology\",\n                    \"telecommunications\"\n                ],\n                \"status\": \"published\",\n                \"createdAt\": \"2025-07-22T13:11:19.988Z\",\n                \"updatedAt\": \"2025-07-22T13:11:19.988Z\",\n                \"company\": {\n                    \"name\": \"MTN Nigeria\"\n                }\n            },\n            {\n                \"id\": 3,\n                \"company_id\": 3,\n                \"publication_id\": 6,\n                \"date\": \"2024-01-17T00:00:00.000Z\",\n                \"headline\": \"GTBank Digital Banking Platform Experiences Downtime\",\n                \"content\": \"Guaranty Trust Bank customers experienced difficulties accessing online banking services due to technical issues with the bank's digital platform.\",\n                \"sentiment\": \"negative\",\n                \"sentiment_score\": \"-0.60\",\n                \"reach\": \"300000\",\n                \"engagement\": 800,\n                \"media_type\": \"online\",\n                \"url\": \"https://nairametrics.com/gtbank-downtime\",\n                \"analyst_id\": 3,\n                \"tags\": [\n                    \"banking\",\n                    \"technology\",\n                    \"downtime\"\n                ],\n                \"status\": \"published\",\n                \"createdAt\": \"2025-07-22T13:11:19.988Z\",\n                \"updatedAt\": \"2025-07-22T13:11:19.988Z\",\n                \"company\": {\n                    \"name\": \"Guaranty Trust Bank\"\n                }\n            },\n            {\n                \"id\": 4,\n                \"company_id\": 4,\n                \"publication_id\": 5,\n                \"date\": \"2024-01-18T00:00:00.000Z\",\n                \"headline\": \"Shoprite Introduces AI-Powered Shopping Experience\",\n                \"content\": \"Shoprite has unveiled new artificial intelligence features in their mobile app to enhance customer shopping experience and improve inventory management.\",\n                \"sentiment\": \"positive\",\n                \"sentiment_score\": \"0.70\",\n                \"reach\": \"150000\",\n                \"engagement\": 600,\n                \"media_type\": \"online\",\n                \"url\": \"https://techcabal.com/shoprite-ai-shopping\",\n                \"analyst_id\": 4,\n                \"tags\": [\n                    \"retail\",\n                    \"AI\",\n                    \"innovation\"\n                ],\n                \"status\": \"published\",\n                \"createdAt\": \"2025-07-22T13:11:19.988Z\",\n                \"updatedAt\": \"2025-07-22T13:11:19.988Z\",\n                \"company\": {\n                    \"name\": \"Shoprite Holdings\"\n                }\n            },\n            {\n                \"id\": 5,\n                \"company_id\": 5,\n                \"publication_id\": 2,\n                \"date\": \"2024-01-19T00:00:00.000Z\",\n                \"headline\": \"Nigerian Breweries Commits to Sustainability Goals\",\n                \"content\": \"Nigerian Breweries Plc has announced new environmental sustainability initiatives aimed at reducing carbon footprint and promoting responsible consumption.\",\n                \"sentiment\": \"positive\",\n                \"sentiment_score\": \"0.60\",\n                \"reach\": \"400000\",\n                \"engagement\": 900,\n                \"media_type\": \"print\",\n                \"url\": \"https://vanguardngr.com/nb-sustainability\",\n                \"analyst_id\": 3,\n                \"tags\": [\n                    \"sustainability\",\n                    \"environment\",\n                    \"CSR\"\n                ],\n                \"status\": \"published\",\n                \"createdAt\": \"2025-07-22T13:11:19.988Z\",\n                \"updatedAt\": \"2025-07-22T13:11:19.988Z\",\n                \"company\": {\n                    \"name\": \"Nigerian Breweries\"\n                }\n            }\n        ]\n    },\n    \"message\": \"Dashboard summary retrieved successfully\"\n}"}]}, {"name": "getMentionsTrend", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/analytics/mentions-trend", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "analytics", "mentions-trend"]}}, "response": [{"name": "getDashboardSummary Copy", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/analytics/mentions-trend", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "analytics", "mentions-trend"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "659"}, {"key": "ETag", "value": "W/\"293-oAKL4TRBLZTjB3WMNUomDODHZWA\""}, {"key": "Date", "value": "<PERSON><PERSON>, 22 Jul 2025 22:43:40 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": [\n        {\n            \"period\": \"2024-01-15T00:00:00.000Z\",\n            \"count\": \"1\",\n            \"sentiment\": \"positive\"\n        },\n        {\n            \"period\": \"2024-01-16T00:00:00.000Z\",\n            \"count\": \"1\",\n            \"sentiment\": \"positive\"\n        },\n        {\n            \"period\": \"2024-01-17T00:00:00.000Z\",\n            \"count\": \"1\",\n            \"sentiment\": \"negative\"\n        },\n        {\n            \"period\": \"2024-01-18T00:00:00.000Z\",\n            \"count\": \"1\",\n            \"sentiment\": \"positive\"\n        },\n        {\n            \"period\": \"2024-01-19T00:00:00.000Z\",\n            \"count\": \"1\",\n            \"sentiment\": \"positive\"\n        },\n        {\n            \"period\": \"2024-01-20T00:00:00.000Z\",\n            \"count\": \"1\",\n            \"sentiment\": \"negative\"\n        },\n        {\n            \"period\": \"2024-01-21T00:00:00.000Z\",\n            \"count\": \"1\",\n            \"sentiment\": \"positive\"\n        },\n        {\n            \"period\": \"2024-01-22T00:00:00.000Z\",\n            \"count\": \"1\",\n            \"sentiment\": \"positive\"\n        }\n    ],\n    \"message\": \"Mentions trend retrieved successfully\"\n}"}]}, {"name": "getSentimentAnalysis", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/analytics/sentiment-analysis", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "analytics", "sentiment-analysis"]}}, "response": [{"name": "getMentionsTrend Copy", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/analytics/sentiment-analysis", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "analytics", "sentiment-analysis"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "749"}, {"key": "ETag", "value": "W/\"2ed-2+MXno9nS+YzwtCykDbGPNlnlnQ\""}, {"key": "Date", "value": "<PERSON><PERSON>, 22 Jul 2025 22:44:19 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"overall\": [\n            {\n                \"sentiment\": \"positive\",\n                \"count\": \"6\",\n                \"avg_score\": \"0.78333333333333333333\"\n            },\n            {\n                \"sentiment\": \"negative\",\n                \"count\": \"2\",\n                \"avg_score\": \"-0.50000000000000000000\"\n            }\n        ],\n        \"byCompany\": [\n            {\n                \"sentiment\": \"positive\",\n                \"count\": \"2\",\n                \"company.name\": \"MTN Nigeria\"\n            },\n            {\n                \"sentiment\": \"negative\",\n                \"count\": \"1\",\n                \"company.name\": \"Dangote Group\"\n            },\n            {\n                \"sentiment\": \"positive\",\n                \"count\": \"1\",\n                \"company.name\": \"Nigerian Breweries\"\n            },\n            {\n                \"sentiment\": \"positive\",\n                \"count\": \"1\",\n                \"company.name\": \"Shoprite Holdings\"\n            },\n            {\n                \"sentiment\": \"positive\",\n                \"count\": \"1\",\n                \"company.name\": \"Guaranty Trust Bank\"\n            },\n            {\n                \"sentiment\": \"negative\",\n                \"count\": \"1\",\n                \"company.name\": \"Guaranty Trust Bank\"\n            },\n            {\n                \"sentiment\": \"positive\",\n                \"count\": \"1\",\n                \"company.name\": \"Dangote Group\"\n            }\n        ]\n    },\n    \"message\": \"Sentiment analysis retrieved successfully\"\n}"}]}, {"name": "getMediaChannelAnalysis", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/analytics/media-channel-analysis", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "analytics", "media-channel-analysis"]}}, "response": [{"name": "getSentimentAnalysis Copy", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/analytics/media-channel-analysis", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "analytics", "media-channel-analysis"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "343"}, {"key": "ETag", "value": "W/\"157-T3XJOTMG4Ef5vwCv7Z5clWFWptg\""}, {"key": "Date", "value": "<PERSON><PERSON>, 22 Jul 2025 22:45:31 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": [\n        {\n            \"media_type\": \"online\",\n            \"count\": \"4\",\n            \"total_reach\": \"1400000\",\n            \"total_engagement\": \"3600\"\n        },\n        {\n            \"media_type\": \"print\",\n            \"count\": \"3\",\n            \"total_reach\": \"1350000\",\n            \"total_engagement\": \"3200\"\n        },\n        {\n            \"media_type\": \"broadcast\",\n            \"count\": \"1\",\n            \"total_reach\": \"2000000\",\n            \"total_engagement\": \"5000\"\n        }\n    ],\n    \"message\": \"Media channel analysis retrieved successfully\"\n}"}]}, {"name": "getCompanyComparison", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/analytics/company-comparison", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "analytics", "company-comparison"]}}, "response": [{"name": "getMediaChannelAnalysis Copy", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/analytics/company-comparison", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "analytics", "company-comparison"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "807"}, {"key": "ETag", "value": "W/\"327-++jRkUtekUpRYkOLfl3TzmC9lUw\""}, {"key": "Date", "value": "<PERSON><PERSON>, 22 Jul 2025 22:46:20 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": [\n        {\n            \"mention_count\": \"2\",\n            \"total_reach\": \"1100000\",\n            \"total_engagement\": \"2700\",\n            \"avg_sentiment\": \"0.20000000000000000000\",\n            \"company.name\": \"Dangote Group\"\n        },\n        {\n            \"mention_count\": \"1\",\n            \"total_reach\": \"400000\",\n            \"total_engagement\": \"900\",\n            \"avg_sentiment\": \"0.60000000000000000000\",\n            \"company.name\": \"Nigerian Breweries\"\n        },\n        {\n            \"mention_count\": \"1\",\n            \"total_reach\": \"150000\",\n            \"total_engagement\": \"600\",\n            \"avg_sentiment\": \"0.70000000000000000000\",\n            \"company.name\": \"Shoprite Holdings\"\n        },\n        {\n            \"mention_count\": \"2\",\n            \"total_reach\": \"2350000\",\n            \"total_engagement\": \"5700\",\n            \"avg_sentiment\": \"0.85000000000000000000\",\n            \"company.name\": \"MTN Nigeria\"\n        },\n        {\n            \"mention_count\": \"2\",\n            \"total_reach\": \"750000\",\n            \"total_engagement\": \"1900\",\n            \"avg_sentiment\": \"0.15000000000000000000\",\n            \"company.name\": \"Guaranty Trust Bank\"\n        }\n    ],\n    \"message\": \"Company comparison retrieved successfully\"\n}"}]}]}, {"name": "AuditLogs", "item": [{"name": "getAuditLogs", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/audit-logs/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "audit-logs", ""]}}, "response": [{"name": "New Request", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/audit-logs/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "audit-logs", ""]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "141"}, {"key": "ETag", "value": "W/\"8d-IZ9cqh6WAMrYfhArdicFQu9yAmA\""}, {"key": "Date", "value": "<PERSON><PERSON>, 22 Jul 2025 22:50:56 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"data\": [],\n        \"pagination\": {\n            \"total\": 0,\n            \"page\": 1,\n            \"limit\": 20,\n            \"totalPages\": 0\n        }\n    },\n    \"message\": \"Audit logs retrieved successfully\"\n}"}]}, {"name": "getAuditLogsStats", "request": {"method": "GET", "header": []}, "response": [{"name": "getAuditLogs Copy", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/audit-logs/stats", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "audit-logs", "stats"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "152"}, {"key": "ETag", "value": "W/\"98-dOEtoPBiAAmMBGEpz1lrV8xMpVI\""}, {"key": "Date", "value": "<PERSON><PERSON>, 22 Jul 2025 22:58:57 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"total\": 0,\n        \"actions\": [],\n        \"severity\": [],\n        \"resources\": [],\n        \"dailyActivity\": []\n    },\n    \"message\": \"Audit log statistics retrieved successfully\"\n}"}]}, {"name": "getAuditLogById", "request": {"method": "GET", "header": []}, "response": [{"name": "getAuditLogsStats Copy", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/audit-logs/1", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "audit-logs", "1"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "61"}, {"key": "ETag", "value": "W/\"3d-wZucf8oacCY0wMxoZajxCQMnF5c\""}, {"key": "Date", "value": "<PERSON><PERSON>, 22 Jul 2025 22:52:17 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": false,\n    \"message\": \"Audit log not found\",\n    \"data\": null\n}"}]}, {"name": "createAuditLogs", "request": {"method": "GET", "header": []}, "response": []}]}, {"name": "CampaignTypes", "item": [{"name": "getCampaignTypes", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/campaign-types/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "campaign-types", ""]}}, "response": [{"name": "New Request", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/campaign-types/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "campaign-types", ""]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "336"}, {"key": "ETag", "value": "W/\"150-d0+s1SUY/bbLFPE5AvjQagCu2+Q\""}, {"key": "Date", "value": "Wed, 23 Jul 2025 01:15:45 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"campaignType\": [\n            {\n                \"id\": 1,\n                \"name\": \"Buns<PERSON>\"\n            }\n        ],\n        \"meta\": {\n            \"total\": 1,\n            \"currentPage\": 1,\n            \"totalPage\": 1,\n            \"pageSize\": 10\n        },\n        \"links\": {\n            \"first\": \"undefined/campaign-types?page=1\",\n            \"last\": \"undefined/campaign-types?page=1\",\n            \"prev\": \"undefined/campaign-types?page=1\",\n            \"next\": \"undefined/campaign-types?pages=1\"\n        }\n    },\n    \"message\": \"All CampaignTypes\"\n}"}]}, {"name": "getCampaignTypeById", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/campaign-types/1", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "campaign-types", "1"]}}, "response": [{"name": "getCampaignTypes Copy", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/campaign-types/1", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "campaign-types", "1"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "78"}, {"key": "ETag", "value": "W/\"4e-p1TYAB3Ybky2E+vUD30K+doBQkg\""}, {"key": "Date", "value": "Wed, 23 Jul 2025 01:12:39 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"id\": 1,\n        \"name\": \"Buns\"\n    },\n    \"message\": \"Single CampaignType\"\n}"}]}, {"name": "createCampaignType", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"Buns\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/campaign-types/create", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "campaign-types", "create"]}}, "response": [{"name": "getCampaignTypeById Copy", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"Buns\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/campaign-types/create", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "campaign-types", "create"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "59"}, {"key": "ETag", "value": "W/\"3b-AVkvJppvrwt8fLZBEHwyJjM03Mk\""}, {"key": "Date", "value": "Wed, 23 Jul 2025 01:17:37 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {},\n    \"message\": \"CampaignType Created\"\n}"}]}, {"name": "updateCampaignType", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"<PERSON><PERSON><PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/campaign-types/update/1", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "campaign-types", "update", "1"]}}, "response": [{"name": "createCampaignType Copy", "originalRequest": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"<PERSON><PERSON><PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/campaign-types/update/1", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "campaign-types", "update", "1"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "59"}, {"key": "ETag", "value": "W/\"3b-PiVgCNrjcqRlh0RRY4vu3b65Cbo\""}, {"key": "Date", "value": "Wed, 23 Jul 2025 01:15:25 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {},\n    \"message\": \"CampaignType Updated\"\n}"}]}, {"name": "deleteCampaignType", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"<PERSON><PERSON><PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/campaign-types/delete/1", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "campaign-types", "delete", "1"]}}, "response": [{"name": "createCampaignType Copy", "originalRequest": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"<PERSON><PERSON><PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/campaign-types/update/1", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "campaign-types", "update", "1"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "59"}, {"key": "ETag", "value": "W/\"3b-PiVgCNrjcqRlh0RRY4vu3b65Cbo\""}, {"key": "Date", "value": "Wed, 23 Jul 2025 01:15:25 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {},\n    \"message\": \"CampaignType Updated\"\n}"}]}]}, {"name": "Channel", "item": [{"name": "getChannels", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"<PERSON><PERSON><PERSON>\"\r\n}"}, "url": {"raw": "http://localhost:3000/api/channels/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "channels", ""]}}, "response": [{"name": "New Request", "originalRequest": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"<PERSON><PERSON><PERSON>\"\r\n}"}, "url": {"raw": "http://localhost:3000/api/channels/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "channels", ""]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "304"}, {"key": "ETag", "value": "W/\"130-foXU0a9FxmO76vCrt/+IxxN2LsA\""}, {"key": "Date", "value": "Wed, 23 Jul 2025 01:35:56 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"channel\": [\n            {\n                \"id\": 2,\n                \"name\": \"Breaking\"\n            }\n        ],\n        \"meta\": {\n            \"total\": 1,\n            \"currentPage\": 1,\n            \"totalPage\": 1,\n            \"pageSize\": 10\n        },\n        \"links\": {\n            \"first\": \"undefined/channels?page=1\",\n            \"last\": \"undefined/channels?page=1\",\n            \"prev\": \"undefined/channels?page=1\",\n            \"next\": \"undefined/channels?pages=1\"\n        }\n    },\n    \"message\": \"All Channels\"\n}"}]}, {"name": "getChannelsById", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/channels/1", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "channels", "1"]}}, "response": [{"name": "getChannels Copy", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/channels/1", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "channels", "1"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "77"}, {"key": "ETag", "value": "W/\"4d-BvaC6HK1wTf4bNNrGZwCaWQmq0k\""}, {"key": "Date", "value": "Wed, 23 Jul 2025 01:31:15 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"id\": 1,\n        \"name\": \"Breaking\"\n    },\n    \"message\": \"Single Channel\"\n}"}]}, {"name": "createChannels", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"Breaking\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/channels/create", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "channels", "create"]}}, "response": [{"name": "getChannelsById Copy", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"Breaking\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/channels/create", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "channels", "create"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "54"}, {"key": "ETag", "value": "W/\"36-+wOG6KEn6c/sH1fMDNQg+jNgqSY\""}, {"key": "Date", "value": "Wed, 23 Jul 2025 01:35:42 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {},\n    \"message\": \"Channel Created\"\n}"}]}, {"name": "updateChannels", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"<PERSON><PERSON><PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/channels/update/1", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "channels", "update", "1"]}}, "response": [{"name": "createChannels Copy", "originalRequest": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"<PERSON><PERSON><PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/channels/update/1", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "channels", "update", "1"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "54"}, {"key": "ETag", "value": "W/\"36-27Wfwnh5wvaKiI4224pp4nQgau4\""}, {"key": "Date", "value": "Wed, 23 Jul 2025 01:34:18 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {},\n    \"message\": \"Channel Updated\"\n}"}]}, {"name": "deleteChannel", "request": {"method": "PUT", "header": [], "url": {"raw": "http://localhost:3000/api/channels/delete/1", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "channels", "delete", "1"]}}, "response": [{"name": "updateChannels Copy", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "http://localhost:3000/api/channels/delete/1", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "channels", "delete", "1"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "54"}, {"key": "ETag", "value": "W/\"36-nQam+LYg+UdwnXx5SFo/KNHs8K8\""}, {"key": "Date", "value": "Wed, 23 Jul 2025 01:35:26 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {},\n    \"message\": \"Channel Deleted\"\n}"}]}]}, {"name": "Company", "item": [{"name": "getCompany", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"<PERSON><PERSON><PERSON>\"\r\n}"}, "url": {"raw": "http://localhost:3000/api/companies/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "companies", ""]}}, "response": [{"name": "New Request", "originalRequest": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"<PERSON><PERSON><PERSON>\"\r\n}"}, "url": {"raw": "http://localhost:3000/api/companies/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "companies", ""]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "6297"}, {"key": "ETag", "value": "W/\"1899-yvMysgnD5sCCOSdV383pScS4EaE\""}, {"key": "Date", "value": "Wed, 23 Jul 2025 02:02:36 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"data\": [\n            {\n                \"id\": 1,\n                \"name\": \"Dangote Group\",\n                \"industry\": \"Conglomerate\",\n                \"sub_industry\": \"Cement, Sugar, Oil & Gas\",\n                \"address\": \"1 Alfred Rewane Road, Ikoyi\",\n                \"state\": \"Lagos\",\n                \"country\": \"Nigeria\",\n                \"email\": \"<EMAIL>\",\n                \"contact\": \"Aliko Dangote\",\n                \"ceo\": \"Aliko Dangote\",\n                \"phone_no\": \"+234-1-448-0815\",\n                \"website\": \"https://www.dangote.com\",\n                \"facebook_link\": \"https://facebook.com/dangotegroup\",\n                \"instagram_link\": \"https://instagram.com/dangotegroup\",\n                \"twitter_link\": \"https://twitter.com/dangotegroup\",\n                \"linkedin_link\": \"https://linkedin.com/company/dangote-group\",\n                \"youtube_link\": null,\n                \"createdAt\": \"2025-07-22T13:06:35.121Z\",\n                \"updatedAt\": \"2025-07-22T13:06:35.121Z\"\n            },\n            {\n                \"id\": 6,\n                \"name\": \"Dangote Group\",\n                \"industry\": \"Conglomerate\",\n                \"sub_industry\": \"Cement, Sugar, Oil & Gas\",\n                \"address\": \"1 Alfred Rewane Road, Ikoyi\",\n                \"state\": \"Lagos\",\n                \"country\": \"Nigeria\",\n                \"email\": \"<EMAIL>\",\n                \"contact\": \"Aliko Dangote\",\n                \"ceo\": \"Aliko Dangote\",\n                \"phone_no\": \"+234-1-448-0815\",\n                \"website\": \"https://www.dangote.com\",\n                \"facebook_link\": \"https://facebook.com/dangotegroup\",\n                \"instagram_link\": \"https://instagram.com/dangotegroup\",\n                \"twitter_link\": \"https://twitter.com/dangotegroup\",\n                \"linkedin_link\": \"https://linkedin.com/company/dangote-group\",\n                \"youtube_link\": null,\n                \"createdAt\": \"2025-07-22T13:11:19.956Z\",\n                \"updatedAt\": \"2025-07-22T13:11:19.956Z\"\n            },\n            {\n                \"id\": 3,\n                \"name\": \"Guaranty Trust Bank\",\n                \"industry\": \"Financial Services\",\n                \"sub_industry\": \"Commercial Banking\",\n                \"address\": \"635 Akin Adesola Street, Victoria Island\",\n                \"state\": \"Lagos\",\n                \"country\": \"Nigeria\",\n                \"email\": \"<EMAIL>\",\n                \"contact\": \"Segun Agbaje\",\n                \"ceo\": \"Segun Agbaje\",\n                \"phone_no\": \"+234-1-448-5500\",\n                \"website\": \"https://www.gtbank.com\",\n                \"facebook_link\": \"https://facebook.com/gtbank\",\n                \"instagram_link\": \"https://instagram.com/gtbank\",\n                \"twitter_link\": \"https://twitter.com/gtbank\",\n                \"linkedin_link\": \"https://linkedin.com/company/guaranty-trust-bank\",\n                \"youtube_link\": null,\n                \"createdAt\": \"2025-07-22T13:06:35.121Z\",\n                \"updatedAt\": \"2025-07-22T13:06:35.121Z\"\n            },\n            {\n                \"id\": 8,\n                \"name\": \"Guaranty Trust Bank\",\n                \"industry\": \"Financial Services\",\n                \"sub_industry\": \"Commercial Banking\",\n                \"address\": \"635 Akin Adesola Street, Victoria Island\",\n                \"state\": \"Lagos\",\n                \"country\": \"Nigeria\",\n                \"email\": \"<EMAIL>\",\n                \"contact\": \"Segun Agbaje\",\n                \"ceo\": \"Segun Agbaje\",\n                \"phone_no\": \"+234-1-448-5500\",\n                \"website\": \"https://www.gtbank.com\",\n                \"facebook_link\": \"https://facebook.com/gtbank\",\n                \"instagram_link\": \"https://instagram.com/gtbank\",\n                \"twitter_link\": \"https://twitter.com/gtbank\",\n                \"linkedin_link\": \"https://linkedin.com/company/guaranty-trust-bank\",\n                \"youtube_link\": null,\n                \"createdAt\": \"2025-07-22T13:11:19.956Z\",\n                \"updatedAt\": \"2025-07-22T13:11:19.956Z\"\n            },\n            {\n                \"id\": 11,\n                \"name\": \"Makarios\",\n                \"industry\": \"Real Estate\",\n                \"sub_industry\": \"Lands\",\n                \"address\": \"No 10\",\n                \"state\": \"Lagos\",\n                \"country\": \"Nigeria\",\n                \"email\": \"<EMAIL>\",\n                \"contact\": \"Name\",\n                \"ceo\": \"Brandy Smooch\",\n                \"phone_no\": \"************\",\n                \"website\": \"www.monkey\",\n                \"facebook_link\": \"www.monkey\",\n                \"instagram_link\": \"www.monkey\",\n                \"twitter_link\": \"www.monkey\",\n                \"linkedin_link\": \"www.monkey\",\n                \"youtube_link\": \"www.monkey\",\n                \"createdAt\": \"2025-07-23T02:02:25.452Z\",\n                \"updatedAt\": \"2025-07-23T02:02:25.452Z\"\n            },\n            {\n                \"id\": 2,\n                \"name\": \"MTN Nigeria\",\n                \"industry\": \"Telecommunications\",\n                \"sub_industry\": \"Mobile Network Operator\",\n                \"address\": \"Churchgate Tower, 30 Afribank Street, Victoria Island\",\n                \"state\": \"Lagos\",\n                \"country\": \"Nigeria\",\n                \"email\": \"<EMAIL>\",\n                \"contact\": \"Karl Toriola\",\n                \"ceo\": \"Karl Toriola\",\n                \"phone_no\": \"+234-************\",\n                \"website\": \"https://www.mtn.ng\",\n                \"facebook_link\": \"https://facebook.com/MTNng\",\n                \"instagram_link\": \"https://instagram.com/mtnng\",\n                \"twitter_link\": \"https://twitter.com/mtnng\",\n                \"linkedin_link\": \"https://linkedin.com/company/mtn-nigeria\",\n                \"youtube_link\": null,\n                \"createdAt\": \"2025-07-22T13:06:35.121Z\",\n                \"updatedAt\": \"2025-07-22T13:06:35.121Z\"\n            },\n            {\n                \"id\": 7,\n                \"name\": \"MTN Nigeria\",\n                \"industry\": \"Telecommunications\",\n                \"sub_industry\": \"Mobile Network Operator\",\n                \"address\": \"Churchgate Tower, 30 Afribank Street, Victoria Island\",\n                \"state\": \"Lagos\",\n                \"country\": \"Nigeria\",\n                \"email\": \"<EMAIL>\",\n                \"contact\": \"Karl Toriola\",\n                \"ceo\": \"Karl Toriola\",\n                \"phone_no\": \"+234-************\",\n                \"website\": \"https://www.mtn.ng\",\n                \"facebook_link\": \"https://facebook.com/MTNng\",\n                \"instagram_link\": \"https://instagram.com/mtnng\",\n                \"twitter_link\": \"https://twitter.com/mtnng\",\n                \"linkedin_link\": \"https://linkedin.com/company/mtn-nigeria\",\n                \"youtube_link\": null,\n                \"createdAt\": \"2025-07-22T13:11:19.956Z\",\n                \"updatedAt\": \"2025-07-22T13:11:19.956Z\"\n            },\n            {\n                \"id\": 5,\n                \"name\": \"Nigerian Breweries\",\n                \"industry\": \"Consumer Goods\",\n                \"sub_industry\": \"Beverages\",\n                \"address\": \"Iganmu Industrial Estate, Surulere\",\n                \"state\": \"Lagos\",\n                \"country\": \"Nigeria\",\n                \"email\": \"<EMAIL>\",\n                \"contact\": \"Hans Essaadi\",\n                \"ceo\": \"Hans Essaadi\",\n                \"phone_no\": \"+234-1-270-2000\",\n                \"website\": \"https://www.nbplc.com\",\n                \"facebook_link\": \"https://facebook.com/nigerianbreweries\",\n                \"instagram_link\": \"https://instagram.com/nigerianbreweries\",\n                \"twitter_link\": \"https://twitter.com/nbplc\",\n                \"linkedin_link\": \"https://linkedin.com/company/nigerian-breweries\",\n                \"youtube_link\": null,\n                \"createdAt\": \"2025-07-22T13:06:35.121Z\",\n                \"updatedAt\": \"2025-07-22T13:06:35.121Z\"\n            },\n            {\n                \"id\": 10,\n                \"name\": \"Nigerian Breweries\",\n                \"industry\": \"Consumer Goods\",\n                \"sub_industry\": \"Beverages\",\n                \"address\": \"Iganmu Industrial Estate, Surulere\",\n                \"state\": \"Lagos\",\n                \"country\": \"Nigeria\",\n                \"email\": \"<EMAIL>\",\n                \"contact\": \"Hans Essaadi\",\n                \"ceo\": \"Hans Essaadi\",\n                \"phone_no\": \"+234-1-270-2000\",\n                \"website\": \"https://www.nbplc.com\",\n                \"facebook_link\": \"https://facebook.com/nigerianbreweries\",\n                \"instagram_link\": \"https://instagram.com/nigerianbreweries\",\n                \"twitter_link\": \"https://twitter.com/nbplc\",\n                \"linkedin_link\": \"https://linkedin.com/company/nigerian-breweries\",\n                \"youtube_link\": null,\n                \"createdAt\": \"2025-07-22T13:11:19.956Z\",\n                \"updatedAt\": \"2025-07-22T13:11:19.956Z\"\n            },\n            {\n                \"id\": 9,\n                \"name\": \"Shoprite Holdings\",\n                \"industry\": \"Retail\",\n                \"sub_industry\": \"Supermarket Chain\",\n                \"address\": \"Ikeja City Mall, Obafemi Awolowo Way\",\n                \"state\": \"Lagos\",\n                \"country\": \"Nigeria\",\n                \"email\": \"<EMAIL>\",\n                \"contact\": \"Pieter Engelbrecht\",\n                \"ceo\": \"Pieter Engelbrecht\",\n                \"phone_no\": \"+234-1-271-9300\",\n                \"website\": \"https://www.shoprite.ng\",\n                \"facebook_link\": \"https://facebook.com/shopriteng\",\n                \"instagram_link\": \"https://instagram.com/shopriteng\",\n                \"twitter_link\": \"https://twitter.com/shopriteng\",\n                \"linkedin_link\": \"https://linkedin.com/company/shoprite-nigeria\",\n                \"youtube_link\": null,\n                \"createdAt\": \"2025-07-22T13:11:19.956Z\",\n                \"updatedAt\": \"2025-07-22T13:11:19.956Z\"\n            }\n        ],\n        \"pagination\": {\n            \"total\": 11,\n            \"page\": 1,\n            \"limit\": 10,\n            \"totalPages\": 2\n        }\n    },\n    \"message\": \"Companies retrieved successfully\"\n}"}]}, {"name": "searchCompany", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/companies/search?q=tech", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "companies", "search"], "query": [{"key": "q", "value": "tech"}]}}, "response": [{"name": "getCompany Copy", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/companies/search?q=tech", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "companies", "search"], "query": [{"key": "q", "value": "tech"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "63"}, {"key": "ETag", "value": "W/\"3f-D4oXqzIG/CwTqWijmPp+4QhA/n0\""}, {"key": "Date", "value": "Wed, 23 Jul 2025 01:53:21 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": [],\n    \"message\": \"Companies search results\"\n}"}]}, {"name": "createCompany", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"Makarios\",\r\n    \"industry\": \"Real Estate\",\r\n    \"sub_industry\": \"Lands\",\r\n    \"address\": \"No 10\",\r\n    \"state\": \"Lagos\",\r\n    \"country\": \"Nigeria\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"contact\": \"Name\",\r\n    \"ceo\": \"<PERSON><PERSON>\",\r\n    \"phone_no\": \"************\",\r\n    \"website\": \"www.monkey\",\r\n    \"facebook_link\": \"www.monkey\",\r\n    \"instagram_link\": \"www.monkey\",\r\n    \"twitter_link\": \"www.monkey\",\r\n    \"linkedin_link\": \"www.monkey\",\r\n    \"youtube_link\": \"www.monkey\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/companies/create", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "companies", "create"]}}, "response": [{"name": "searchCompany Copy", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"Makarios\",\r\n    \"industry\": \"Real Estate\",\r\n    \"sub_industry\": \"Lands\",\r\n    \"address\": \"No 10\",\r\n    \"state\": \"Lagos\",\r\n    \"country\": \"Nigeria\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"contact\": \"Name\",\r\n    \"ceo\": \"<PERSON><PERSON>\",\r\n    \"phone_no\": \"************\",\r\n    \"website\": \"www.monkey\",\r\n    \"facebook_link\": \"www.monkey\",\r\n    \"instagram_link\": \"www.monkey\",\r\n    \"twitter_link\": \"www.monkey\",\r\n    \"linkedin_link\": \"www.monkey\",\r\n    \"youtube_link\": \"www.monkey\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/companies/create", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "companies", "create"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "54"}, {"key": "ETag", "value": "W/\"36-dvzk6WmlDjHSMuslMpgW0uH/A7c\""}, {"key": "Date", "value": "Wed, 23 Jul 2025 02:02:25 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {},\n    \"message\": \"Company Created\"\n}"}]}, {"name": "getCompanyById", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/companies/1", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "companies", "1"]}}, "response": [{"name": "createCompany Copy", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/companies/1", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "companies", "1"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "592"}, {"key": "ETag", "value": "W/\"250-nyF1GdVy8zredK8dooQXhaIvFNU\""}, {"key": "Date", "value": "Wed, 23 Jul 2025 02:04:18 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"id\": 1,\n        \"name\": \"Dangote Group\",\n        \"industry\": \"Conglomerate\",\n        \"sub_industry\": \"Cement, Sugar, Oil & Gas\",\n        \"address\": \"1 Alfred Rewane Road, Ikoyi\",\n        \"subsidiary\": [],\n        \"state\": \"Lagos\",\n        \"country\": \"Nigeria\",\n        \"contact\": \"Aliko Dangote\",\n        \"ceo\": \"Aliko Dangote\",\n        \"phone_no\": \"+234-1-448-0815\",\n        \"website\": \"https://www.dangote.com\",\n        \"facebook_link\": \"https://facebook.com/dangotegroup\",\n        \"instagram_link\": \"https://instagram.com/dangotegroup\",\n        \"twitter_link\": \"https://twitter.com/dangotegroup\",\n        \"linkedin_link\": \"https://linkedin.com/company/dangote-group\",\n        \"youtube_link\": null\n    },\n    \"message\": \"Single Company\"\n}"}]}, {"name": "updateCompany", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"Notorious Group\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/companies/update/11", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "companies", "update", "11"]}}, "response": [{"name": "updateCompany", "originalRequest": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"Notorious Group\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/companies/update/11", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "companies", "update", "11"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "54"}, {"key": "ETag", "value": "W/\"36-zmD4AzYJAg1BWWIkuiOutYUv3AY\""}, {"key": "Date", "value": "Wed, 23 Jul 2025 06:25:30 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {},\n    \"message\": \"Company Updated\"\n}"}]}, {"name": "deleteCompany", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"Notorious Group\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/companies/delete/11", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "companies", "delete", "11"]}}, "response": [{"name": "deleteCompany", "originalRequest": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"Notorious Group\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/companies/delete/11", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "companies", "delete", "11"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "54"}, {"key": "ETag", "value": "W/\"36-naT90VSrJSbYvzAwOlDOOxcYRXA\""}, {"key": "Date", "value": "Wed, 23 Jul 2025 06:29:21 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {},\n    \"message\": \"Company Deleted\"\n}"}]}]}, {"name": "DailyMentions", "item": [{"name": "getDailyMentions", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"password123\"\r\n}"}, "url": {"raw": "http://localhost:3000/api/daily-mentions/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "daily-mentions", ""]}}, "response": [{"name": "getDailyMentions", "originalRequest": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"password123\"\r\n}"}, "url": {"raw": "http://localhost:3000/api/daily-mentions/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "daily-mentions", ""]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "6437"}, {"key": "ETag", "value": "W/\"1925-5q79z6oE1cfWn0Wt8/ktMTabhek\""}, {"key": "Date", "value": "Thu, 24 Jul 2025 06:23:02 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"data\": [\n            {\n                \"id\": 18,\n                \"company_id\": 1,\n                \"publication_id\": 2,\n                \"date\": \"2025-07-24T00:00:00.000Z\",\n                \"headline\": \"Sample Headline2\",\n                \"content\": \"Sample content for the daily mention.\",\n                \"sentiment\": \"positive\",\n                \"sentiment_score\": \"0.85\",\n                \"reach\": \"10000\",\n                \"engagement\": 500,\n                \"media_type\": \"online\",\n                \"url\": \"https://example.com/article\",\n                \"analyst_id\": 1,\n                \"tags\": [\n                    \"tag1\",\n                    \"tag2\"\n                ],\n                \"status\": \"draft\",\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-24T06:22:14.839Z\",\n                \"updatedAt\": \"2025-07-24T06:22:14.839Z\",\n                \"company\": {\n                    \"id\": 1,\n                    \"name\": \"Dangote Group\",\n                    \"industry\": \"Conglomerate\"\n                },\n                \"publication\": {\n                    \"id\": 2,\n                    \"name\": \"Vanguard\",\n                    \"type\": \"Newspaper\"\n                },\n                \"analyst\": {\n                    \"id\": 1,\n                    \"username\": \"admin\",\n                    \"email\": \"<EMAIL>\"\n                }\n            },\n            {\n                \"id\": 10,\n                \"company_id\": 1,\n                \"publication_id\": 2,\n                \"date\": \"2025-07-24T00:00:00.000Z\",\n                \"headline\": \"Sample Headline\",\n                \"content\": \"Sample content for the daily mention.\",\n                \"sentiment\": \"positive\",\n                \"sentiment_score\": \"0.85\",\n                \"reach\": \"10000\",\n                \"engagement\": 500,\n                \"media_type\": \"online\",\n                \"url\": \"https://example.com/article\",\n                \"analyst_id\": 1,\n                \"tags\": [\n                    \"tag1\",\n                    \"tag2\"\n                ],\n                \"status\": \"draft\",\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-24T05:56:21.071Z\",\n                \"updatedAt\": \"2025-07-24T05:56:21.071Z\",\n                \"company\": {\n                    \"id\": 1,\n                    \"name\": \"Dangote Group\",\n                    \"industry\": \"Conglomerate\"\n                },\n                \"publication\": {\n                    \"id\": 2,\n                    \"name\": \"Vanguard\",\n                    \"type\": \"Newspaper\"\n                },\n                \"analyst\": {\n                    \"id\": 1,\n                    \"username\": \"admin\",\n                    \"email\": \"<EMAIL>\"\n                }\n            },\n            {\n                \"id\": 11,\n                \"company_id\": 1,\n                \"publication_id\": null,\n                \"date\": \"2025-07-24T00:00:00.000Z\",\n                \"headline\": \"Sample Headline\",\n                \"content\": \"Sample content for the daily mention.\",\n                \"sentiment\": \"positive\",\n                \"sentiment_score\": \"0.85\",\n                \"reach\": \"10000\",\n                \"engagement\": 500,\n                \"media_type\": \"online\",\n                \"url\": \"https://example.com/article\",\n                \"analyst_id\": 1,\n                \"tags\": [\n                    \"tag1\",\n                    \"tag2\"\n                ],\n                \"status\": \"draft\",\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-24T06:03:37.152Z\",\n                \"updatedAt\": \"2025-07-24T06:03:37.152Z\",\n                \"company\": {\n                    \"id\": 1,\n                    \"name\": \"Dangote Group\",\n                    \"industry\": \"Conglomerate\"\n                },\n                \"publication\": null,\n                \"analyst\": {\n                    \"id\": 1,\n                    \"username\": \"admin\",\n                    \"email\": \"<EMAIL>\"\n                }\n            },\n            {\n                \"id\": 12,\n                \"company_id\": 1,\n                \"publication_id\": 2,\n                \"date\": \"2025-07-24T00:00:00.000Z\",\n                \"headline\": \"Sample Headline\",\n                \"content\": null,\n                \"sentiment\": \"positive\",\n                \"sentiment_score\": \"0.85\",\n                \"reach\": \"10000\",\n                \"engagement\": 500,\n                \"media_type\": \"online\",\n                \"url\": \"https://example.com/article\",\n                \"analyst_id\": 1,\n                \"tags\": [\n                    \"tag1\",\n                    \"tag2\"\n                ],\n                \"status\": \"draft\",\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-24T06:04:39.632Z\",\n                \"updatedAt\": \"2025-07-24T06:04:39.632Z\",\n                \"company\": {\n                    \"id\": 1,\n                    \"name\": \"Dangote Group\",\n                    \"industry\": \"Conglomerate\"\n                },\n                \"publication\": {\n                    \"id\": 2,\n                    \"name\": \"Vanguard\",\n                    \"type\": \"Newspaper\"\n                },\n                \"analyst\": {\n                    \"id\": 1,\n                    \"username\": \"admin\",\n                    \"email\": \"<EMAIL>\"\n                }\n            },\n            {\n                \"id\": 13,\n                \"company_id\": 1,\n                \"publication_id\": 2,\n                \"date\": \"2025-07-24T00:00:00.000Z\",\n                \"headline\": \"Sample Headline\",\n                \"content\": \"Sample content for the daily mention.\",\n                \"sentiment\": \"neutral\",\n                \"sentiment_score\": \"0.85\",\n                \"reach\": \"10000\",\n                \"engagement\": 500,\n                \"media_type\": \"online\",\n                \"url\": \"https://example.com/article\",\n                \"analyst_id\": 1,\n                \"tags\": [\n                    \"tag1\",\n                    \"tag2\"\n                ],\n                \"status\": \"draft\",\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-24T06:04:55.061Z\",\n                \"updatedAt\": \"2025-07-24T06:04:55.061Z\",\n                \"company\": {\n                    \"id\": 1,\n                    \"name\": \"Dangote Group\",\n                    \"industry\": \"Conglomerate\"\n                },\n                \"publication\": {\n                    \"id\": 2,\n                    \"name\": \"Vanguard\",\n                    \"type\": \"Newspaper\"\n                },\n                \"analyst\": {\n                    \"id\": 1,\n                    \"username\": \"admin\",\n                    \"email\": \"<EMAIL>\"\n                }\n            },\n            {\n                \"id\": 14,\n                \"company_id\": 1,\n                \"publication_id\": 2,\n                \"date\": \"2025-07-24T00:00:00.000Z\",\n                \"headline\": \"Sample Headline\",\n                \"content\": \"Sample content for the daily mention.\",\n                \"sentiment\": \"positive\",\n                \"sentiment_score\": null,\n                \"reach\": \"10000\",\n                \"engagement\": 500,\n                \"media_type\": \"online\",\n                \"url\": \"https://example.com/article\",\n                \"analyst_id\": 1,\n                \"tags\": [\n                    \"tag1\",\n                    \"tag2\"\n                ],\n                \"status\": \"draft\",\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-24T06:05:09.210Z\",\n                \"updatedAt\": \"2025-07-24T06:05:09.210Z\",\n                \"company\": {\n                    \"id\": 1,\n                    \"name\": \"Dangote Group\",\n                    \"industry\": \"Conglomerate\"\n                },\n                \"publication\": {\n                    \"id\": 2,\n                    \"name\": \"Vanguard\",\n                    \"type\": \"Newspaper\"\n                },\n                \"analyst\": {\n                    \"id\": 1,\n                    \"username\": \"admin\",\n                    \"email\": \"<EMAIL>\"\n                }\n            },\n            {\n                \"id\": 15,\n                \"company_id\": 1,\n                \"publication_id\": 2,\n                \"date\": \"2025-07-24T00:00:00.000Z\",\n                \"headline\": \"Sample Headline\",\n                \"content\": \"Sample content for the daily mention.\",\n                \"sentiment\": \"positive\",\n                \"sentiment_score\": \"0.85\",\n                \"reach\": \"0\",\n                \"engagement\": 500,\n                \"media_type\": \"online\",\n                \"url\": \"https://example.com/article\",\n                \"analyst_id\": 1,\n                \"tags\": [\n                    \"tag1\",\n                    \"tag2\"\n                ],\n                \"status\": \"draft\",\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-24T06:05:23.036Z\",\n                \"updatedAt\": \"2025-07-24T06:05:23.036Z\",\n                \"company\": {\n                    \"id\": 1,\n                    \"name\": \"Dangote Group\",\n                    \"industry\": \"Conglomerate\"\n                },\n                \"publication\": {\n                    \"id\": 2,\n                    \"name\": \"Vanguard\",\n                    \"type\": \"Newspaper\"\n                },\n                \"analyst\": {\n                    \"id\": 1,\n                    \"username\": \"admin\",\n                    \"email\": \"<EMAIL>\"\n                }\n            },\n            {\n                \"id\": 16,\n                \"company_id\": 1,\n                \"publication_id\": 2,\n                \"date\": \"2025-07-24T00:00:00.000Z\",\n                \"headline\": \"Sample Headline\",\n                \"content\": \"Sample content for the daily mention.\",\n                \"sentiment\": \"positive\",\n                \"sentiment_score\": \"0.85\",\n                \"reach\": \"10000\",\n                \"engagement\": 0,\n                \"media_type\": \"online\",\n                \"url\": \"https://example.com/article\",\n                \"analyst_id\": 1,\n                \"tags\": [\n                    \"tag1\",\n                    \"tag2\"\n                ],\n                \"status\": \"draft\",\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-24T06:05:31.726Z\",\n                \"updatedAt\": \"2025-07-24T06:05:31.726Z\",\n                \"company\": {\n                    \"id\": 1,\n                    \"name\": \"Dangote Group\",\n                    \"industry\": \"Conglomerate\"\n                },\n                \"publication\": {\n                    \"id\": 2,\n                    \"name\": \"Vanguard\",\n                    \"type\": \"Newspaper\"\n                },\n                \"analyst\": {\n                    \"id\": 1,\n                    \"username\": \"admin\",\n                    \"email\": \"<EMAIL>\"\n                }\n            },\n            {\n                \"id\": 17,\n                \"company_id\": 1,\n                \"publication_id\": 2,\n                \"date\": \"2025-07-24T00:00:00.000Z\",\n                \"headline\": \"Sample Headline\",\n                \"content\": \"Sample content for the daily mention.\",\n                \"sentiment\": \"positive\",\n                \"sentiment_score\": \"0.85\",\n                \"reach\": \"10000\",\n                \"engagement\": 500,\n                \"media_type\": \"online\",\n                \"url\": null,\n                \"analyst_id\": 1,\n                \"tags\": [\n                    \"tag1\",\n                    \"tag2\"\n                ],\n                \"status\": \"draft\",\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-24T06:05:54.832Z\",\n                \"updatedAt\": \"2025-07-24T06:05:54.832Z\",\n                \"company\": {\n                    \"id\": 1,\n                    \"name\": \"Dangote Group\",\n                    \"industry\": \"Conglomerate\"\n                },\n                \"publication\": {\n                    \"id\": 2,\n                    \"name\": \"Vanguard\",\n                    \"type\": \"Newspaper\"\n                },\n                \"analyst\": {\n                    \"id\": 1,\n                    \"username\": \"admin\",\n                    \"email\": \"<EMAIL>\"\n                }\n            },\n            {\n                \"id\": 9,\n                \"company_id\": 1,\n                \"publication_id\": 2,\n                \"date\": \"2025-07-24T00:00:00.000Z\",\n                \"headline\": \"Sample Headline\",\n                \"content\": \"Sample content for the daily mention.\",\n                \"sentiment\": \"positive\",\n                \"sentiment_score\": \"0.85\",\n                \"reach\": \"10000\",\n                \"engagement\": 500,\n                \"media_type\": \"online\",\n                \"url\": \"https://example.com/article\",\n                \"analyst_id\": null,\n                \"tags\": [\n                    \"tag1\",\n                    \"tag2\"\n                ],\n                \"status\": \"draft\",\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-24T05:48:01.676Z\",\n                \"updatedAt\": \"2025-07-24T05:48:01.676Z\",\n                \"company\": {\n                    \"id\": 1,\n                    \"name\": \"Dangote Group\",\n                    \"industry\": \"Conglomerate\"\n                },\n                \"publication\": {\n                    \"id\": 2,\n                    \"name\": \"Vanguard\",\n                    \"type\": \"Newspaper\"\n                },\n                \"analyst\": null\n            }\n        ],\n        \"pagination\": {\n            \"total\": 18,\n            \"page\": 1,\n            \"limit\": 10,\n            \"totalPages\": 2\n        }\n    },\n    \"message\": \"Daily mentions retrieved successfully\"\n}"}]}, {"name": "createDailyMentions", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsImlhdCI6MTc1MzI5MTE0NywiZXhwIjoxNzUzMzc3NTQ3fQ.rl6XpUKpVyphsc2T3vekeLcbmHI-cK37lt9qyTT2yeQ", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"company_id\": 1,\r\n  \"publication_id\": 2,\r\n  \"date\": \"2025-07-24T00:00:00Z\",\r\n  \"headline\": \"Sample Headline2\",\r\n  \"content\": \"Sample content for the daily mention.\",\r\n  \"sentiment\": \"positive\",\r\n  \"sentiment_score\": 0.85,\r\n  \"reach\": 10000,\r\n  \"engagement\": 500,\r\n  \"media_type\": \"online\",\r\n  \"url\": \"https://example.com/article\",\r\n  \"tags\": [\"tag1\", \"tag2\"],\r\n  \"status\": \"draft\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/daily-mentions/create", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "daily-mentions", "create"]}}, "response": [{"name": "createDailyMentions", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"company_id\": 1,\r\n  \"publication_id\": 2,\r\n  \"date\": \"2025-07-24T00:00:00Z\",\r\n  \"headline\": \"Sample Headline2\",\r\n  \"content\": \"Sample content for the daily mention.\",\r\n  \"sentiment\": \"positive\",\r\n  \"sentiment_score\": 0.85,\r\n  \"reach\": 10000,\r\n  \"engagement\": 500,\r\n  \"media_type\": \"online\",\r\n  \"url\": \"https://example.com/article\",\r\n  \"tags\": [\"tag1\", \"tag2\"],\r\n  \"status\": \"draft\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/daily-mentions/create", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "daily-mentions", "create"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "716"}, {"key": "ETag", "value": "W/\"2cc-UxqCFk1ixgRlQ8WAMOdgjU9INoo\""}, {"key": "Date", "value": "Thu, 24 Jul 2025 06:22:14 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"id\": 18,\n        \"company_id\": 1,\n        \"publication_id\": 2,\n        \"date\": \"2025-07-24T00:00:00.000Z\",\n        \"headline\": \"Sample Headline2\",\n        \"content\": \"Sample content for the daily mention.\",\n        \"sentiment\": \"positive\",\n        \"sentiment_score\": \"0.85\",\n        \"reach\": \"10000\",\n        \"engagement\": 500,\n        \"media_type\": \"online\",\n        \"url\": \"https://example.com/article\",\n        \"analyst_id\": 1,\n        \"tags\": [\n            \"tag1\",\n            \"tag2\"\n        ],\n        \"status\": \"draft\",\n        \"is_deleted\": false,\n        \"createdAt\": \"2025-07-24T06:22:14.839Z\",\n        \"updatedAt\": \"2025-07-24T06:22:14.839Z\",\n        \"company\": {\n            \"id\": 1,\n            \"name\": \"Dangote Group\",\n            \"industry\": \"Conglomerate\"\n        },\n        \"publication\": {\n            \"id\": 2,\n            \"name\": \"Vanguard\",\n            \"type\": \"Newspaper\"\n        },\n        \"analyst\": {\n            \"id\": 1,\n            \"username\": \"admin\",\n            \"email\": \"<EMAIL>\"\n        }\n    },\n    \"message\": \"Daily mention created successfully\"\n}"}]}, {"name": "getDailyMentionsById", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsImlhdCI6MTc1MzI5MTE0NywiZXhwIjoxNzUzMzc3NTQ3fQ.rl6XpUKpVyphsc2T3vekeLcbmHI-cK37lt9qyTT2yeQ", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"company_id\": 1,\r\n  \"publication_id\": 2,\r\n  \"date\": \"2025-07-24T00:00:00Z\",\r\n  \"headline\": \"Sample Headline2\",\r\n  \"content\": \"Sample content for the daily mention.\",\r\n  \"sentiment\": \"positive\",\r\n  \"sentiment_score\": 0.85,\r\n  \"reach\": 10000,\r\n  \"engagement\": 500,\r\n  \"media_type\": \"online\",\r\n  \"url\": \"https://example.com/article\",\r\n  \"tags\": [\"tag1\", \"tag2\"],\r\n  \"status\": \"draft\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/daily-mentions/1", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "daily-mentions", "1"]}}, "response": [{"name": "getDailyMentionsById", "originalRequest": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"company_id\": 1,\r\n  \"publication_id\": 2,\r\n  \"date\": \"2025-07-24T00:00:00Z\",\r\n  \"headline\": \"Sample Headline2\",\r\n  \"content\": \"Sample content for the daily mention.\",\r\n  \"sentiment\": \"positive\",\r\n  \"sentiment_score\": 0.85,\r\n  \"reach\": 10000,\r\n  \"engagement\": 500,\r\n  \"media_type\": \"online\",\r\n  \"url\": \"https://example.com/article\",\r\n  \"tags\": [\"tag1\", \"tag2\"],\r\n  \"status\": \"draft\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/daily-mentions/1", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "daily-mentions", "1"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "899"}, {"key": "ETag", "value": "W/\"383-gWYoyNfur4ylgOekWILycScu3N4\""}, {"key": "Date", "value": "Thu, 24 Jul 2025 06:25:44 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"id\": 1,\n        \"company_id\": 1,\n        \"publication_id\": 1,\n        \"date\": \"2024-01-15T00:00:00.000Z\",\n        \"headline\": \"Dangote Cement Reports Strong Q4 Performance\",\n        \"content\": \"Dangote Cement Plc has announced impressive fourth-quarter results, showing significant growth in revenue and market expansion across West Africa.\",\n        \"sentiment\": \"positive\",\n        \"sentiment_score\": \"0.80\",\n        \"reach\": \"500000\",\n        \"engagement\": 1200,\n        \"media_type\": \"print\",\n        \"url\": \"https://punchng.com/dangote-cement-q4-results\",\n        \"analyst_id\": 3,\n        \"tags\": [\n            \"earnings\",\n            \"cement\",\n            \"growth\"\n        ],\n        \"status\": \"published\",\n        \"is_deleted\": false,\n        \"createdAt\": \"2025-07-23T17:15:17.286Z\",\n        \"updatedAt\": \"2025-07-23T17:15:17.286Z\",\n        \"company\": {\n            \"id\": 1,\n            \"name\": \"Dangote Group\",\n            \"industry\": \"Conglomerate\"\n        },\n        \"publication\": {\n            \"id\": 1,\n            \"name\": \"The Punch\",\n            \"type\": \"Newspaper\"\n        },\n        \"analyst\": {\n            \"id\": 3,\n            \"username\": \"analyst1\",\n            \"email\": \"<EMAIL>\"\n        }\n    },\n    \"message\": \"Daily mention retrieved successfully\"\n}"}]}, {"name": "updateDailyMentions", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsImlhdCI6MTc1MzI5MTE0NywiZXhwIjoxNzUzMzc3NTQ3fQ.rl6XpUKpVyphsc2T3vekeLcbmHI-cK37lt9qyTT2yeQ", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"content\": \"Sample content for the daily mentions.\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/daily-mentions/update/11", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "daily-mentions", "update", "11"]}}, "response": [{"name": "updateDailyMentions", "originalRequest": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"content\": \"Sample content for the daily mentions.\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/daily-mentions/update/11", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "daily-mentions", "update", "11"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "678"}, {"key": "ETag", "value": "W/\"2a6-4NQXe3RrWCkefZOPjTz2p7L9LDg\""}, {"key": "Date", "value": "Thu, 24 Jul 2025 06:30:30 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"id\": 11,\n        \"company_id\": 1,\n        \"publication_id\": null,\n        \"date\": \"2025-07-24T00:00:00.000Z\",\n        \"headline\": \"Sample Headline\",\n        \"content\": \"Sample content for the daily mentions.\",\n        \"sentiment\": \"positive\",\n        \"sentiment_score\": \"0.85\",\n        \"reach\": \"10000\",\n        \"engagement\": 500,\n        \"media_type\": \"online\",\n        \"url\": \"https://example.com/article\",\n        \"analyst_id\": 1,\n        \"tags\": [\n            \"tag1\",\n            \"tag2\"\n        ],\n        \"status\": \"draft\",\n        \"is_deleted\": false,\n        \"createdAt\": \"2025-07-24T06:03:37.152Z\",\n        \"updatedAt\": \"2025-07-24T06:30:30.250Z\",\n        \"company\": {\n            \"id\": 1,\n            \"name\": \"Dangote Group\",\n            \"industry\": \"Conglomerate\"\n        },\n        \"publication\": null,\n        \"analyst\": {\n            \"id\": 1,\n            \"username\": \"admin\",\n            \"email\": \"<EMAIL>\"\n        }\n    },\n    \"message\": \"Daily mention updated successfully\"\n}"}]}, {"name": "deleteDailyMentions", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsImlhdCI6MTc1MzI5MTE0NywiZXhwIjoxNzUzMzc3NTQ3fQ.rl6XpUKpVyphsc2T3vekeLcbmHI-cK37lt9qyTT2yeQ", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"content\": \"Sample content for the daily mentions.\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/daily-mentions/delete/11", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "daily-mentions", "delete", "11"]}}, "response": [{"name": "deleteDailyMention", "originalRequest": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"content\": \"Sample content for the daily mentions.\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/daily-mentions/delete/11", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "daily-mentions", "delete", "11"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "73"}, {"key": "ETag", "value": "W/\"49-rsyWavFAdM1eiVe35I2bI3vzeyI\""}, {"key": "Date", "value": "Thu, 24 Jul 2025 06:50:27 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {},\n    \"message\": \"Daily mention deleted successfully\"\n}"}]}]}, {"name": "DataEntry", "item": [{"name": "getAll", "request": {"method": "GET", "header": []}, "response": [{"name": "getAllDatEntries", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/data-entries/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "data-entries", ""]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "4831"}, {"key": "ETag", "value": "W/\"12df-iaHaa4I86TEQCM7gsQesWTm/2aU\""}, {"key": "Date", "value": "Thu, 24 Jul 2025 13:26:18 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"data\": [\n            {\n                \"id\": 11,\n                \"user_id\": 1,\n                \"company_id\": 1,\n                \"entry_type\": \"editorial\",\n                \"title\": \"Sample Entrye\",\n                \"data\": {\n                    \"yes\": \"no\"\n                },\n                \"status\": \"draft\",\n                \"submission_date\": \"2025-07-24T00:00:00.000Z\",\n                \"review_date\": \"2025-07-25T00:00:00.000Z\",\n                \"reviewer_id\": 2,\n                \"review_notes\": \"Some notes\",\n                \"metadata\": {\n                    \"yes\": \"no\"\n                },\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-24T13:26:02.163Z\",\n                \"updatedAt\": \"2025-07-24T13:26:02.163Z\",\n                \"user\": {\n                    \"id\": 1,\n                    \"username\": \"admin\",\n                    \"email\": \"<EMAIL>\"\n                },\n                \"company\": {\n                    \"id\": 1,\n                    \"name\": \"Dangote Group\"\n                }\n            },\n            {\n                \"id\": 10,\n                \"user_id\": 1,\n                \"company_id\": 1,\n                \"entry_type\": \"editorial\",\n                \"title\": \"Sample Entry\",\n                \"data\": {\n                    \"yes\": \"no\"\n                },\n                \"status\": \"draft\",\n                \"submission_date\": \"2025-07-24T00:00:00.000Z\",\n                \"review_date\": \"2025-07-25T00:00:00.000Z\",\n                \"reviewer_id\": null,\n                \"review_notes\": \"Some notes\",\n                \"metadata\": {\n                    \"yes\": \"no\"\n                },\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-24T13:19:46.209Z\",\n                \"updatedAt\": \"2025-07-24T13:19:46.209Z\",\n                \"user\": {\n                    \"id\": 1,\n                    \"username\": \"admin\",\n                    \"email\": \"<EMAIL>\"\n                },\n                \"company\": {\n                    \"id\": 1,\n                    \"name\": \"Dangote Group\"\n                }\n            },\n            {\n                \"id\": 9,\n                \"user_id\": 1,\n                \"company_id\": 1,\n                \"entry_type\": \"editorial\",\n                \"title\": \"Sample Entry\",\n                \"data\": {\n                    \"yes\": \"no\"\n                },\n                \"status\": \"draft\",\n                \"submission_date\": \"2025-07-24T00:00:00.000Z\",\n                \"review_date\": \"2025-07-25T00:00:00.000Z\",\n                \"reviewer_id\": 2,\n                \"review_notes\": \"Some notes\",\n                \"metadata\": {},\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-24T13:15:53.794Z\",\n                \"updatedAt\": \"2025-07-24T13:15:53.794Z\",\n                \"user\": {\n                    \"id\": 1,\n                    \"username\": \"admin\",\n                    \"email\": \"<EMAIL>\"\n                },\n                \"company\": {\n                    \"id\": 1,\n                    \"name\": \"Dangote Group\"\n                }\n            },\n            {\n                \"id\": 8,\n                \"user_id\": 1,\n                \"company_id\": 1,\n                \"entry_type\": \"editorial\",\n                \"title\": \"Sample Entry\",\n                \"data\": {\n                    \"yes\": \"no\"\n                },\n                \"status\": \"draft\",\n                \"submission_date\": \"2025-07-24T00:00:00.000Z\",\n                \"review_date\": \"2025-07-25T00:00:00.000Z\",\n                \"reviewer_id\": 2,\n                \"review_notes\": null,\n                \"metadata\": {\n                    \"yes\": \"no\"\n                },\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-24T13:15:29.593Z\",\n                \"updatedAt\": \"2025-07-24T13:15:29.593Z\",\n                \"user\": {\n                    \"id\": 1,\n                    \"username\": \"admin\",\n                    \"email\": \"<EMAIL>\"\n                },\n                \"company\": {\n                    \"id\": 1,\n                    \"name\": \"Dangote Group\"\n                }\n            },\n            {\n                \"id\": 7,\n                \"user_id\": 1,\n                \"company_id\": 1,\n                \"entry_type\": \"editorial\",\n                \"title\": \"Sample Entry\",\n                \"data\": {\n                    \"yes\": \"no\"\n                },\n                \"status\": \"draft\",\n                \"submission_date\": null,\n                \"review_date\": null,\n                \"reviewer_id\": 2,\n                \"review_notes\": \"Some notes\",\n                \"metadata\": {\n                    \"yes\": \"no\"\n                },\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-24T13:15:21.864Z\",\n                \"updatedAt\": \"2025-07-24T13:15:21.864Z\",\n                \"user\": {\n                    \"id\": 1,\n                    \"username\": \"admin\",\n                    \"email\": \"<EMAIL>\"\n                },\n                \"company\": {\n                    \"id\": 1,\n                    \"name\": \"Dangote Group\"\n                }\n            },\n            {\n                \"id\": 6,\n                \"user_id\": 1,\n                \"company_id\": 1,\n                \"entry_type\": \"editorial\",\n                \"title\": \"Sample Entry\",\n                \"data\": {\n                    \"yes\": \"no\"\n                },\n                \"status\": \"draft\",\n                \"submission_date\": null,\n                \"review_date\": \"2025-07-25T00:00:00.000Z\",\n                \"reviewer_id\": 2,\n                \"review_notes\": \"Some notes\",\n                \"metadata\": {\n                    \"yes\": \"no\"\n                },\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-24T13:15:14.150Z\",\n                \"updatedAt\": \"2025-07-24T13:15:14.150Z\",\n                \"user\": {\n                    \"id\": 1,\n                    \"username\": \"admin\",\n                    \"email\": \"<EMAIL>\"\n                },\n                \"company\": {\n                    \"id\": 1,\n                    \"name\": \"Dangote Group\"\n                }\n            },\n            {\n                \"id\": 5,\n                \"user_id\": 1,\n                \"company_id\": 1,\n                \"entry_type\": \"editorial\",\n                \"title\": \"Sample Entry\",\n                \"data\": {\n                    \"yes\": \"no\"\n                },\n                \"status\": \"draft\",\n                \"submission_date\": \"2025-07-24T00:00:00.000Z\",\n                \"review_date\": \"2025-07-25T00:00:00.000Z\",\n                \"reviewer_id\": 2,\n                \"review_notes\": \"Some notes\",\n                \"metadata\": {\n                    \"yes\": \"no\"\n                },\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-24T13:14:57.516Z\",\n                \"updatedAt\": \"2025-07-24T13:14:57.516Z\",\n                \"user\": {\n                    \"id\": 1,\n                    \"username\": \"admin\",\n                    \"email\": \"<EMAIL>\"\n                },\n                \"company\": {\n                    \"id\": 1,\n                    \"name\": \"Dangote Group\"\n                }\n            },\n            {\n                \"id\": 4,\n                \"user_id\": 1,\n                \"company_id\": 1,\n                \"entry_type\": \"editorial\",\n                \"title\": \"Sample Entry\",\n                \"data\": {},\n                \"status\": \"draft\",\n                \"submission_date\": \"2025-07-24T00:00:00.000Z\",\n                \"review_date\": \"2025-07-25T00:00:00.000Z\",\n                \"reviewer_id\": 2,\n                \"review_notes\": \"Some notes\",\n                \"metadata\": {\n                    \"yes\": \"no\"\n                },\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-24T13:14:47.749Z\",\n                \"updatedAt\": \"2025-07-24T13:14:47.749Z\",\n                \"user\": {\n                    \"id\": 1,\n                    \"username\": \"admin\",\n                    \"email\": \"<EMAIL>\"\n                },\n                \"company\": {\n                    \"id\": 1,\n                    \"name\": \"Dangote Group\"\n                }\n            },\n            {\n                \"id\": 3,\n                \"user_id\": 1,\n                \"company_id\": null,\n                \"entry_type\": \"editorial\",\n                \"title\": \"Sample Entry\",\n                \"data\": {\n                    \"yes\": \"no\"\n                },\n                \"status\": \"draft\",\n                \"submission_date\": \"2025-07-24T00:00:00.000Z\",\n                \"review_date\": \"2025-07-25T00:00:00.000Z\",\n                \"reviewer_id\": 2,\n                \"review_notes\": \"Some notes\",\n                \"metadata\": {\n                    \"yes\": \"no\"\n                },\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-24T13:14:18.998Z\",\n                \"updatedAt\": \"2025-07-24T13:14:18.998Z\",\n                \"user\": {\n                    \"id\": 1,\n                    \"username\": \"admin\",\n                    \"email\": \"<EMAIL>\"\n                },\n                \"company\": null\n            },\n            {\n                \"id\": 2,\n                \"user_id\": 1,\n                \"company_id\": 1,\n                \"entry_type\": \"editorial\",\n                \"title\": \"Sample Entry\",\n                \"data\": {\n                    \"yes\": \"no\"\n                },\n                \"status\": \"draft\",\n                \"submission_date\": \"2025-07-24T00:00:00.000Z\",\n                \"review_date\": \"2025-07-25T00:00:00.000Z\",\n                \"reviewer_id\": 2,\n                \"review_notes\": \"Some notes\",\n                \"metadata\": {\n                    \"yes\": \"no\"\n                },\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-24T13:06:40.535Z\",\n                \"updatedAt\": \"2025-07-24T13:06:40.535Z\",\n                \"user\": {\n                    \"id\": 1,\n                    \"username\": \"admin\",\n                    \"email\": \"<EMAIL>\"\n                },\n                \"company\": {\n                    \"id\": 1,\n                    \"name\": \"Dangote Group\"\n                }\n            }\n        ],\n        \"pagination\": {\n            \"total\": 11,\n            \"page\": 1,\n            \"limit\": 10,\n            \"totalPages\": 2\n        }\n    },\n    \"message\": \"Data entries retrieved successfully\"\n}"}]}, {"name": "createDataEntry", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsImlhdCI6MTc1MzI5MTE0NywiZXhwIjoxNzUzMzc3NTQ3fQ.rl6XpUKpVyphsc2T3vekeLcbmHI-cK37lt9qyTT2yeQ", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"company_id\": 1,     \r\n  \"entry_type\": \"editorial\",\r\n  \"title\": \"Sample Entrye\",      \r\n  \"data\": {\r\n    \"yes\": \"no\"\r\n  },                     \r\n  \"status\": \"draft\", \r\n  \"submission_date\": \"2025-07-24T00:00:00Z\",\r\n  \"review_date\": \"2025-07-25T00:00:00Z\",  \r\n  \"reviewer_id\": 2,              \r\n  \"review_notes\": \"Some notes\", \r\n  \"metadata\": {\r\n    \"yes\": \"no\"\r\n  }                  \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/data-entries/create", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "data-entries", "create"]}}, "response": [{"name": "createDataEntry", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"company_id\": 1,     \r\n  \"entry_type\": \"editorial\",\r\n  \"title\": \"Sample Entrye\",      \r\n  \"data\": {\r\n    \"yes\": \"no\"\r\n  },                     \r\n  \"status\": \"draft\", \r\n  \"submission_date\": \"2025-07-24T00:00:00Z\",\r\n  \"review_date\": \"2025-07-25T00:00:00Z\",  \r\n  \"reviewer_id\": 2,              \r\n  \"review_notes\": \"Some notes\", \r\n  \"metadata\": {\r\n    \"yes\": \"no\"\r\n  }                  \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/data-entries/create", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "data-entries", "create"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "549"}, {"key": "ETag", "value": "W/\"225-WsNbvlaxqSi8PVysoAfA9uP3GEM\""}, {"key": "Date", "value": "Thu, 24 Jul 2025 13:26:02 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"id\": 11,\n        \"user_id\": 1,\n        \"company_id\": 1,\n        \"entry_type\": \"editorial\",\n        \"title\": \"Sample Entrye\",\n        \"data\": {\n            \"yes\": \"no\"\n        },\n        \"status\": \"draft\",\n        \"submission_date\": \"2025-07-24T00:00:00.000Z\",\n        \"review_date\": \"2025-07-25T00:00:00.000Z\",\n        \"reviewer_id\": 2,\n        \"review_notes\": \"Some notes\",\n        \"metadata\": {\n            \"yes\": \"no\"\n        },\n        \"is_deleted\": false,\n        \"createdAt\": \"2025-07-24T13:26:02.163Z\",\n        \"updatedAt\": \"2025-07-24T13:26:02.163Z\",\n        \"user\": {\n            \"id\": 1,\n            \"username\": \"admin\",\n            \"email\": \"<EMAIL>\"\n        },\n        \"company\": {\n            \"id\": 1,\n            \"name\": \"Dangote Group\"\n        }\n    },\n    \"message\": \"Data entry created successfully\"\n}"}]}, {"name": "getDataEntryById", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsImlhdCI6MTc1MzI5MTE0NywiZXhwIjoxNzUzMzc3NTQ3fQ.rl6XpUKpVyphsc2T3vekeLcbmHI-cK37lt9qyTT2yeQ", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"company_id\": 1,     \r\n  \"entry_type\": \"editorial\",\r\n  \"title\": \"Sample Entrye\",      \r\n  \"data\": {\r\n    \"yes\": \"no\"\r\n  },                     \r\n  \"status\": \"draft\", \r\n  \"submission_date\": \"2025-07-24T00:00:00Z\",\r\n  \"review_date\": \"2025-07-25T00:00:00Z\",  \r\n  \"reviewer_id\": 2,              \r\n  \"review_notes\": \"Some notes\", \r\n  \"metadata\": {\r\n    \"yes\": \"no\"\r\n  }                  \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/data-entries/1", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "data-entries", "1"]}}, "response": [{"name": "getDataEntryById", "originalRequest": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"company_id\": 1,     \r\n  \"entry_type\": \"editorial\",\r\n  \"title\": \"Sample Entrye\",      \r\n  \"data\": {\r\n    \"yes\": \"no\"\r\n  },                     \r\n  \"status\": \"draft\", \r\n  \"submission_date\": \"2025-07-24T00:00:00Z\",\r\n  \"review_date\": \"2025-07-25T00:00:00Z\",  \r\n  \"reviewer_id\": 2,              \r\n  \"review_notes\": \"Some notes\", \r\n  \"metadata\": {\r\n    \"yes\": \"no\"\r\n  }                  \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/data-entries/1", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "data-entries", "1"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "529"}, {"key": "ETag", "value": "W/\"211-zlpCNrw9DiAnyc1uNvVxBYGhk9A\""}, {"key": "Date", "value": "Thu, 24 Jul 2025 17:52:48 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"id\": 1,\n        \"user_id\": 1,\n        \"company_id\": 1,\n        \"entry_type\": \"editorial\",\n        \"title\": \"Sample Entry\",\n        \"data\": {},\n        \"status\": \"draft\",\n        \"submission_date\": \"2025-07-24T00:00:00.000Z\",\n        \"review_date\": \"2025-07-25T00:00:00.000Z\",\n        \"reviewer_id\": 2,\n        \"review_notes\": \"Some notes\",\n        \"metadata\": {},\n        \"is_deleted\": false,\n        \"createdAt\": \"2025-07-24T12:58:23.343Z\",\n        \"updatedAt\": \"2025-07-24T12:58:23.343Z\",\n        \"user\": {\n            \"id\": 1,\n            \"username\": \"admin\",\n            \"email\": \"<EMAIL>\"\n        },\n        \"company\": {\n            \"id\": 1,\n            \"name\": \"Dangote Group\"\n        }\n    },\n    \"message\": \"Data entry retrieved successfully\"\n}"}]}, {"name": "updateDataEntry", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsImlhdCI6MTc1MzI5MTE0NywiZXhwIjoxNzUzMzc3NTQ3fQ.rl6XpUKpVyphsc2T3vekeLcbmHI-cK37lt9qyTT2yeQ", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"company_id\": 1,     \r\n  \"entry_type\": \"editorial\",\r\n  \"title\": \"Sample Entrya\"                                     \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/data-entries/update/1", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "data-entries", "update", "1"]}}, "response": [{"name": "updateDataEntry", "originalRequest": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"company_id\": 1,     \r\n  \"entry_type\": \"editorial\",\r\n  \"title\": \"Sample Entrya\"                                     \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/data-entries/update/1", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "data-entries", "update", "1"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "528"}, {"key": "ETag", "value": "W/\"210-ohxatyPMIsx84/uGp8VuY/mnaHA\""}, {"key": "Date", "value": "Thu, 24 Jul 2025 17:58:14 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"id\": 1,\n        \"user_id\": 1,\n        \"company_id\": 1,\n        \"entry_type\": \"editorial\",\n        \"title\": \"Sample Entrya\",\n        \"data\": {},\n        \"status\": \"draft\",\n        \"submission_date\": \"2025-07-24T00:00:00.000Z\",\n        \"review_date\": \"2025-07-25T00:00:00.000Z\",\n        \"reviewer_id\": 2,\n        \"review_notes\": \"Some notes\",\n        \"metadata\": {},\n        \"is_deleted\": false,\n        \"createdAt\": \"2025-07-24T12:58:23.343Z\",\n        \"updatedAt\": \"2025-07-24T17:58:14.040Z\",\n        \"user\": {\n            \"id\": 1,\n            \"username\": \"admin\",\n            \"email\": \"<EMAIL>\"\n        },\n        \"company\": {\n            \"id\": 1,\n            \"name\": \"Dangote Group\"\n        }\n    },\n    \"message\": \"Data entry updated successfully\"\n}"}]}, {"name": "deleteDataEntry", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsImlhdCI6MTc1MzI5MTE0NywiZXhwIjoxNzUzMzc3NTQ3fQ.rl6XpUKpVyphsc2T3vekeLcbmHI-cK37lt9qyTT2yeQ", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"company_id\": 1,     \r\n  \"entry_type\": \"editorial\",\r\n  \"title\": \"Sample Entrya\"                                     \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/data-entries/delete/2", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "data-entries", "delete", "2"]}}, "response": [{"name": "deleteDataEntry", "originalRequest": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"company_id\": 1,     \r\n  \"entry_type\": \"editorial\",\r\n  \"title\": \"Sample Entrya\"                                     \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/data-entries/delete/2", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "data-entries", "delete", "2"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "70"}, {"key": "ETag", "value": "W/\"46-eAanfefF9SCaEGoWLp6CTl3v9Ro\""}, {"key": "Date", "value": "Thu, 24 Jul 2025 18:08:42 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {},\n    \"message\": \"Data entry deleted successfully\"\n}"}]}, {"name": "submitDataEntry", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsImlhdCI6MTc1MzI5MTE0NywiZXhwIjoxNzUzMzc3NTQ3fQ.rl6XpUKpVyphsc2T3vekeLcbmHI-cK37lt9qyTT2yeQ", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"company_id\": 1,     \r\n  \"entry_type\": \"editorial\",\r\n  \"title\": \"Sample Entrya\"                                     \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/data-entries/3/submit", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "data-entries", "3", "submit"]}}, "response": [{"name": "submitDataEntry", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"company_id\": 1,     \r\n  \"entry_type\": \"editorial\",\r\n  \"title\": \"Sample Entrya\"                                     \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/data-entries/3/submit", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "data-entries", "3", "submit"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "448"}, {"key": "ETag", "value": "W/\"1c0-DlpZO+lziX0RGuPnDJ2blJUulc4\""}, {"key": "Date", "value": "Fri, 25 Jul 2025 00:10:10 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"id\": 3,\n        \"user_id\": 1,\n        \"company_id\": null,\n        \"entry_type\": \"editorial\",\n        \"title\": \"Sample Entry\",\n        \"data\": {\n            \"yes\": \"no\"\n        },\n        \"status\": \"submitted\",\n        \"submission_date\": \"2025-07-25T00:10:10.154Z\",\n        \"review_date\": \"2025-07-25T00:00:00.000Z\",\n        \"reviewer_id\": 2,\n        \"review_notes\": \"Some notes\",\n        \"metadata\": {\n            \"yes\": \"no\"\n        },\n        \"is_deleted\": false,\n        \"createdAt\": \"2025-07-24T13:14:18.998Z\",\n        \"updatedAt\": \"2025-07-25T00:10:10.157Z\"\n    },\n    \"message\": \"Data entry submitted successfully\"\n}"}]}, {"name": "approveDataEntry", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsImlhdCI6MTc1MzQwMzYzMywiZXhwIjoxNzUzNDkwMDMzfQ.TLLp0fk7JcNmMKGfU8vwOxNqK89XsEgbetXSvnjumOY", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"company_id\": 1,     \r\n  \"entry_type\": \"editorial\",\r\n  \"title\": \"Sample Entrya\"                                     \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/data-entries/3/approve", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "data-entries", "3", "approve"]}}, "response": [{"name": "appproveDataEntry", "originalRequest": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"company_id\": 1,     \r\n  \"entry_type\": \"editorial\",\r\n  \"title\": \"Sample Entrya\"                                     \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/data-entries/3/approve", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "data-entries", "3", "approve"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "446"}, {"key": "ETag", "value": "W/\"1be-CB8WezfWeHEmRX5RdEx5hpfEUFk\""}, {"key": "Date", "value": "Fri, 25 Jul 2025 00:36:11 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"id\": 3,\n        \"user_id\": 1,\n        \"company_id\": null,\n        \"entry_type\": \"editorial\",\n        \"title\": \"Sample Entry\",\n        \"data\": {\n            \"yes\": \"no\"\n        },\n        \"status\": \"approved\",\n        \"submission_date\": \"2025-07-25T00:11:07.304Z\",\n        \"review_date\": \"2025-07-25T00:36:11.353Z\",\n        \"reviewer_id\": 1,\n        \"review_notes\": \"Some notes\",\n        \"metadata\": {\n            \"yes\": \"no\"\n        },\n        \"is_deleted\": false,\n        \"createdAt\": \"2025-07-24T13:14:18.998Z\",\n        \"updatedAt\": \"2025-07-25T00:36:11.355Z\"\n    },\n    \"message\": \"Data entry approved successfully\"\n}"}]}, {"name": "rejectDataEntry", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsImlhdCI6MTc1MzQwMzYzMywiZXhwIjoxNzUzNDkwMDMzfQ.TLLp0fk7JcNmMKGfU8vwOxNqK89XsEgbetXSvnjumOY", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"company_id\": 1,     \r\n  \"entry_type\": \"editorial\",\r\n  \"title\": \"Sample Entrya\"                                     \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/data-entries/3/reject", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "data-entries", "3", "reject"]}}, "response": [{"name": "rejectDataEntry", "originalRequest": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"company_id\": 1,     \r\n  \"entry_type\": \"editorial\",\r\n  \"title\": \"Sample Entrya\"                                     \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/data-entries/3/reject", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "data-entries", "3", "reject"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "446"}, {"key": "ETag", "value": "W/\"1be-PIgRMvJln2a9noFASMwJ8BHiMHs\""}, {"key": "Date", "value": "Fri, 25 Jul 2025 00:44:04 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"id\": 3,\n        \"user_id\": 1,\n        \"company_id\": null,\n        \"entry_type\": \"editorial\",\n        \"title\": \"Sample Entry\",\n        \"data\": {\n            \"yes\": \"no\"\n        },\n        \"status\": \"rejected\",\n        \"submission_date\": \"2025-07-25T00:11:07.304Z\",\n        \"review_date\": \"2025-07-25T00:44:04.037Z\",\n        \"reviewer_id\": 1,\n        \"review_notes\": \"Some notes\",\n        \"metadata\": {\n            \"yes\": \"no\"\n        },\n        \"is_deleted\": false,\n        \"createdAt\": \"2025-07-24T13:14:18.998Z\",\n        \"updatedAt\": \"2025-07-25T00:44:04.038Z\"\n    },\n    \"message\": \"Data entry rejected successfully\"\n}"}]}]}, {"name": "DataParameters", "item": [{"name": "getAllParameters", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/data-parameters/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "data-parameters", ""]}}, "response": [{"name": "gatAllDataParameters", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/data-parameters/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "data-parameters", ""]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "2660"}, {"key": "ETag", "value": "W/\"a64-2jBl0UeCf1kbmL2ta5cMRyaTwWc\""}, {"key": "Date", "value": "Fri, 25 Jul 2025 01:17:02 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"data\": [\n            {\n                \"id\": 1,\n                \"name\": \"Positive\",\n                \"value\": \"positive\",\n                \"category\": \"sentiment\",\n                \"description\": \"Positive sentiment mentions\",\n                \"is_active\": true,\n                \"sort_order\": 1,\n                \"metadata\": {},\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-23T17:15:17.271Z\",\n                \"updatedAt\": \"2025-07-23T17:15:17.271Z\"\n            },\n            {\n                \"id\": 2,\n                \"name\": \"Negative\",\n                \"value\": \"negative\",\n                \"category\": \"sentiment\",\n                \"description\": \"Negative sentiment mentions\",\n                \"is_active\": true,\n                \"sort_order\": 2,\n                \"metadata\": {},\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-23T17:15:17.271Z\",\n                \"updatedAt\": \"2025-07-23T17:15:17.271Z\"\n            },\n            {\n                \"id\": 3,\n                \"name\": \"Neutral\",\n                \"value\": \"neutral\",\n                \"category\": \"sentiment\",\n                \"description\": \"Neutral sentiment mentions\",\n                \"is_active\": true,\n                \"sort_order\": 3,\n                \"metadata\": {},\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-23T17:15:17.271Z\",\n                \"updatedAt\": \"2025-07-23T17:15:17.271Z\"\n            },\n            {\n                \"id\": 4,\n                \"name\": \"Print\",\n                \"value\": \"print\",\n                \"category\": \"media_type\",\n                \"description\": \"Print media publications\",\n                \"is_active\": true,\n                \"sort_order\": 1,\n                \"metadata\": {},\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-23T17:15:17.271Z\",\n                \"updatedAt\": \"2025-07-23T17:15:17.271Z\"\n            },\n            {\n                \"id\": 5,\n                \"name\": \"Online\",\n                \"value\": \"online\",\n                \"category\": \"media_type\",\n                \"description\": \"Online media publications\",\n                \"is_active\": true,\n                \"sort_order\": 2,\n                \"metadata\": {},\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-23T17:15:17.271Z\",\n                \"updatedAt\": \"2025-07-23T17:15:17.271Z\"\n            },\n            {\n                \"id\": 6,\n                \"name\": \"Broadcast\",\n                \"value\": \"broadcast\",\n                \"category\": \"media_type\",\n                \"description\": \"Television and radio broadcasts\",\n                \"is_active\": true,\n                \"sort_order\": 3,\n                \"metadata\": {},\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-23T17:15:17.271Z\",\n                \"updatedAt\": \"2025-07-23T17:15:17.271Z\"\n            },\n            {\n                \"id\": 7,\n                \"name\": \"Social Media\",\n                \"value\": \"social\",\n                \"category\": \"media_type\",\n                \"description\": \"Social media platforms\",\n                \"is_active\": true,\n                \"sort_order\": 4,\n                \"metadata\": {},\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-23T17:15:17.271Z\",\n                \"updatedAt\": \"2025-07-23T17:15:17.271Z\"\n            },\n            {\n                \"id\": 8,\n                \"name\": \"Draft\",\n                \"value\": \"draft\",\n                \"category\": \"status\",\n                \"description\": \"Draft status for content\",\n                \"is_active\": true,\n                \"sort_order\": 1,\n                \"metadata\": {},\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-23T17:15:17.271Z\",\n                \"updatedAt\": \"2025-07-23T17:15:17.271Z\"\n            },\n            {\n                \"id\": 9,\n                \"name\": \"Pending\",\n                \"value\": \"pending\",\n                \"category\": \"status\",\n                \"description\": \"Pending approval status\",\n                \"is_active\": true,\n                \"sort_order\": 2,\n                \"metadata\": {},\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-23T17:15:17.271Z\",\n                \"updatedAt\": \"2025-07-23T17:15:17.271Z\"\n            },\n            {\n                \"id\": 10,\n                \"name\": \"Approved\",\n                \"value\": \"approved\",\n                \"category\": \"status\",\n                \"description\": \"Approved status\",\n                \"is_active\": true,\n                \"sort_order\": 3,\n                \"metadata\": {},\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-23T17:15:17.271Z\",\n                \"updatedAt\": \"2025-07-23T17:15:17.271Z\"\n            }\n        ],\n        \"pagination\": {\n            \"total\": 21,\n            \"page\": 1,\n            \"limit\": 10,\n            \"totalPages\": 3\n        }\n    },\n    \"message\": \"Data parameters retrieved successfully\"\n}"}]}, {"name": "getAllParametersCategories", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/data-parameters/categories", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "data-parameters", "categories"]}}, "response": [{"name": "getParametersByCategory", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/data-parameters/categories", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "data-parameters", "categories"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "132"}, {"key": "ETag", "value": "W/\"84-m0024TQN0OYklSlmIjfF5OOwAWo\""}, {"key": "Date", "value": "Fri, 25 Jul 2025 01:26:16 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": [\n        \"media_type\",\n        \"industry\",\n        \"Category Name\",\n        \"status\",\n        \"sentiment\"\n    ],\n    \"message\": \"Categories retrieved successfully\"\n}"}]}, {"name": "getDataParameterByCategory", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/data-parameters/category/status", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "data-parameters", "category", "status"]}}, "response": [{"name": "getAllParametersCategories Copy", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/data-parameters/category/status", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "data-parameters", "category", "status"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "1540"}, {"key": "ETag", "value": "W/\"604-T3vBL40YCbkcBOoe0L9Nvh74yYw\""}, {"key": "Date", "value": "Fri, 25 Jul 2025 01:17:49 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": [\n        {\n            \"id\": 8,\n            \"name\": \"Draft\",\n            \"value\": \"draft\",\n            \"category\": \"status\",\n            \"description\": \"Draft status for content\",\n            \"is_active\": true,\n            \"sort_order\": 1,\n            \"metadata\": {},\n            \"is_deleted\": false,\n            \"createdAt\": \"2025-07-23T17:15:17.271Z\",\n            \"updatedAt\": \"2025-07-23T17:15:17.271Z\"\n        },\n        {\n            \"id\": 9,\n            \"name\": \"Pending\",\n            \"value\": \"pending\",\n            \"category\": \"status\",\n            \"description\": \"Pending approval status\",\n            \"is_active\": true,\n            \"sort_order\": 2,\n            \"metadata\": {},\n            \"is_deleted\": false,\n            \"createdAt\": \"2025-07-23T17:15:17.271Z\",\n            \"updatedAt\": \"2025-07-23T17:15:17.271Z\"\n        },\n        {\n            \"id\": 10,\n            \"name\": \"Approved\",\n            \"value\": \"approved\",\n            \"category\": \"status\",\n            \"description\": \"Approved status\",\n            \"is_active\": true,\n            \"sort_order\": 3,\n            \"metadata\": {},\n            \"is_deleted\": false,\n            \"createdAt\": \"2025-07-23T17:15:17.271Z\",\n            \"updatedAt\": \"2025-07-23T17:15:17.271Z\"\n        },\n        {\n            \"id\": 11,\n            \"name\": \"Rejected\",\n            \"value\": \"rejected\",\n            \"category\": \"status\",\n            \"description\": \"Rejected status\",\n            \"is_active\": true,\n            \"sort_order\": 4,\n            \"metadata\": {},\n            \"is_deleted\": false,\n            \"createdAt\": \"2025-07-23T17:15:17.271Z\",\n            \"updatedAt\": \"2025-07-23T17:15:17.271Z\"\n        },\n        {\n            \"id\": 12,\n            \"name\": \"Published\",\n            \"value\": \"published\",\n            \"category\": \"status\",\n            \"description\": \"Published status\",\n            \"is_active\": true,\n            \"sort_order\": 5,\n            \"metadata\": {},\n            \"is_deleted\": false,\n            \"createdAt\": \"2025-07-23T17:15:17.271Z\",\n            \"updatedAt\": \"2025-07-23T17:15:17.271Z\"\n        },\n        {\n            \"id\": 13,\n            \"name\": \"Archived\",\n            \"value\": \"archived\",\n            \"category\": \"status\",\n            \"description\": \"Archived status\",\n            \"is_active\": true,\n            \"sort_order\": 6,\n            \"metadata\": {},\n            \"is_deleted\": false,\n            \"createdAt\": \"2025-07-23T17:15:17.271Z\",\n            \"updatedAt\": \"2025-07-23T17:15:17.271Z\"\n        }\n    ],\n    \"message\": \"status parameters retrieved successfully\"\n}"}]}, {"name": "createDataParameter", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"Parameter Name\",        \r\n  \"value\": \"Parameter Value\",     \r\n  \"category\": \"Category Name\",    \r\n  \"description\": \"Optional description\", \r\n  \"is_active\": true, \r\n  \"sort_order\": 1, \r\n  \"metadata\": { \"key\": \"value\" }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/data-parameters/create", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "data-parameters", "create"]}}, "response": [{"name": "getDataParameterByCategory Copy", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"Parameter Name\",        \r\n  \"value\": \"Parameter Value\",     \r\n  \"category\": \"Category Name\",    \r\n  \"description\": \"Optional description\", \r\n  \"is_active\": true, \r\n  \"sort_order\": 1, \r\n  \"metadata\": { \"key\": \"value\" }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/data-parameters/create", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "data-parameters", "create"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "351"}, {"key": "ETag", "value": "W/\"15f-fts7G5UJRZddWJQMaRr3vNVRl04\""}, {"key": "Date", "value": "Fri, 25 Jul 2025 01:30:09 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"is_deleted\": false,\n        \"id\": 23,\n        \"name\": \"Parameter Name\",\n        \"value\": \"Parameter Value\",\n        \"category\": \"Category Name\",\n        \"description\": \"Optional description\",\n        \"is_active\": true,\n        \"sort_order\": 1,\n        \"metadata\": {\n            \"key\": \"value\"\n        },\n        \"updatedAt\": \"2025-07-25T01:30:08.973Z\",\n        \"createdAt\": \"2025-07-25T01:30:08.973Z\"\n    },\n    \"message\": \"Data parameter created successfully\"\n}"}]}, {"name": "getDataParameterById", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/data-parameters/1", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "data-parameters", "1"]}}, "response": [{"name": "createDataParameter Copy", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/data-parameters/1", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "data-parameters", "1"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "326"}, {"key": "ETag", "value": "W/\"146-voS2u4SYQinKz/IQS+XkJyp/JsU\""}, {"key": "Date", "value": "Fri, 25 Jul 2025 01:32:27 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"id\": 1,\n        \"name\": \"kolos\",\n        \"value\": \"positive\",\n        \"category\": \"sentiment\",\n        \"description\": \"Positive sentiment mentions\",\n        \"is_active\": true,\n        \"sort_order\": 1,\n        \"metadata\": {},\n        \"is_deleted\": false,\n        \"createdAt\": \"2025-07-23T17:15:17.271Z\",\n        \"updatedAt\": \"2025-07-25T01:32:18.919Z\"\n    },\n    \"message\": \"Data parameter retrieved successfully\"\n}"}]}, {"name": "updateDataParameters", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"kolos\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/data-parameters/update/1", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "data-parameters", "update", "1"]}}, "response": [{"name": "getDataParameterById Copy", "originalRequest": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"kolos\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/data-parameters/update/1", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "data-parameters", "update", "1"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "324"}, {"key": "ETag", "value": "W/\"144-R84BeTvfUqsN6OzrIkL9rSA1OKE\""}, {"key": "Date", "value": "Fri, 25 Jul 2025 01:32:18 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"id\": 1,\n        \"name\": \"kolos\",\n        \"value\": \"positive\",\n        \"category\": \"sentiment\",\n        \"description\": \"Positive sentiment mentions\",\n        \"is_active\": true,\n        \"sort_order\": 1,\n        \"metadata\": {},\n        \"is_deleted\": false,\n        \"createdAt\": \"2025-07-23T17:15:17.271Z\",\n        \"updatedAt\": \"2025-07-25T01:32:18.919Z\"\n    },\n    \"message\": \"Data parameter updated successfully\"\n}"}]}, {"name": "deleteDataParameter", "request": {"method": "PUT", "header": [], "url": {"raw": "http://localhost:3000/api/data-parameters/delete/6", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "data-parameters", "delete", "6"]}}, "response": [{"name": "updateDataParameters Copy", "originalRequest": {"method": "PUT", "header": [], "url": {"raw": "http://localhost:3000/api/data-parameters/delete/6", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "data-parameters", "delete", "6"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "74"}, {"key": "ETag", "value": "W/\"4a-ZlaXWVlCDvoUS1JA4Ins33OJ0MU\""}, {"key": "Date", "value": "Fri, 25 Jul 2025 01:33:52 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {},\n    \"message\": \"Data parameter deleted successfully\"\n}"}]}]}, {"name": "Editorials", "item": [{"name": "getAllEditorials", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/editorials/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "editorials", ""]}}, "response": [{"name": "getAll", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/editorials/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "editorials", ""]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "3827"}, {"key": "ETag", "value": "W/\"ef3-gHaZdB1sBvtSqkIj+o0xdMgUWWE\""}, {"key": "Date", "value": "Fri, 25 Jul 2025 10:53:28 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"editorial\": [\n            {\n                \"id\": 1,\n                \"date\": \"2025-07-25T00:00:00.000Z\",\n                \"media_type\": {\n                    \"id\": 1,\n                    \"name\": \"Print\",\n                    \"is_deleted\": false,\n                    \"createdAt\": \"2025-07-25T10:31:48.991Z\",\n                    \"updatedAt\": \"2025-07-25T10:31:48.991Z\"\n                },\n                \"company\": {\n                    \"id\": 2,\n                    \"name\": \"MTN Nigeria\",\n                    \"industry\": \"Telecommunications\",\n                    \"sub_industry\": \"Mobile Network Operator\",\n                    \"address\": \"Churchgate Tower, 30 Afribank Street, Victoria Island\",\n                    \"state\": \"Lagos\",\n                    \"country\": \"Nigeria\",\n                    \"email\": \"<EMAIL>\",\n                    \"contact\": \"<PERSON>\",\n                    \"ceo\": \"<PERSON>\",\n                    \"phone_no\": \"+234-************\",\n                    \"website\": \"https://www.mtn.ng\",\n                    \"facebook_link\": \"https://facebook.com/MTNng\",\n                    \"instagram_link\": \"https://instagram.com/mtnng\",\n                    \"twitter_link\": \"https://twitter.com/mtnng\",\n                    \"linkedin_link\": \"https://linkedin.com/company/mtn-nigeria\",\n                    \"youtube_link\": null,\n                    \"is_deleted\": false,\n                    \"createdAt\": \"2025-07-23T17:15:17.251Z\",\n                    \"updatedAt\": \"2025-07-23T17:15:17.251Z\"\n                },\n                \"brand\": \"BrandX\",\n                \"industry\": \"Technology\",\n                \"sub_sector\": \"Software\",\n                \"publication\": {\n                    \"id\": 3,\n                    \"name\": \"ThisDay\",\n                    \"type\": \"Newspaper\",\n                    \"website\": \"https://thisdaylive.com\",\n                    \"description\": \"Nigerian daily newspaper\",\n                    \"circulation\": 100000,\n                    \"reach\": 1500000,\n                    \"location\": \"Lagos, Nigeria\",\n                    \"is_deleted\": false,\n                    \"createdAt\": \"2025-07-23T17:15:17.261Z\",\n                    \"updatedAt\": \"2025-07-23T17:15:17.261Z\"\n                },\n                \"placement\": {\n                    \"id\": 1,\n                    \"name\": \"Advert\",\n                    \"is_deleted\": false,\n                    \"createdAt\": \"2025-07-25T10:36:15.025Z\",\n                    \"updatedAt\": \"2025-07-25T10:36:15.025Z\"\n                },\n                \"title\": \"Editorial Title Example\",\n                \"page_number\": 12,\n                \"link\": \"https://example.com/editorial-article\",\n                \"reporter\": \"Jane Doe\",\n                \"country\": \"Nigeria\",\n                \"spokesperson\": \"John Smith\",\n                \"activity\": {\n                    \"id\": 1,\n                    \"name\": \"Money\",\n                    \"is_deleted\": false,\n                    \"createdAt\": \"2025-07-25T10:38:29.430Z\",\n                    \"updatedAt\": \"2025-07-25T10:38:29.430Z\"\n                },\n                \"sentiment\": \"positive\",\n                \"media_sentiment_index\": 3,\n                \"advert_spend\": 100000,\n                \"circulation\": 50000,\n                \"page_size\": \"Full Page\"\n            },\n            {\n                \"id\": 2,\n                \"date\": \"2025-07-25T00:00:00.000Z\",\n                \"media_type\": {\n                    \"id\": 1,\n                    \"name\": \"Print\",\n                    \"is_deleted\": false,\n                    \"createdAt\": \"2025-07-25T10:31:48.991Z\",\n                    \"updatedAt\": \"2025-07-25T10:31:48.991Z\"\n                },\n                \"company\": {\n                    \"id\": 2,\n                    \"name\": \"MTN Nigeria\",\n                    \"industry\": \"Telecommunications\",\n                    \"sub_industry\": \"Mobile Network Operator\",\n                    \"address\": \"Churchgate Tower, 30 Afribank Street, Victoria Island\",\n                    \"state\": \"Lagos\",\n                    \"country\": \"Nigeria\",\n                    \"email\": \"<EMAIL>\",\n                    \"contact\": \"Karl Toriola\",\n                    \"ceo\": \"Karl Toriola\",\n                    \"phone_no\": \"+234-************\",\n                    \"website\": \"https://www.mtn.ng\",\n                    \"facebook_link\": \"https://facebook.com/MTNng\",\n                    \"instagram_link\": \"https://instagram.com/mtnng\",\n                    \"twitter_link\": \"https://twitter.com/mtnng\",\n                    \"linkedin_link\": \"https://linkedin.com/company/mtn-nigeria\",\n                    \"youtube_link\": null,\n                    \"is_deleted\": false,\n                    \"createdAt\": \"2025-07-23T17:15:17.251Z\",\n                    \"updatedAt\": \"2025-07-23T17:15:17.251Z\"\n                },\n                \"brand\": \"BrandX\",\n                \"industry\": \"Technology\",\n                \"sub_sector\": \"Software\",\n                \"publication\": {\n                    \"id\": 3,\n                    \"name\": \"ThisDay\",\n                    \"type\": \"Newspaper\",\n                    \"website\": \"https://thisdaylive.com\",\n                    \"description\": \"Nigerian daily newspaper\",\n                    \"circulation\": 100000,\n                    \"reach\": 1500000,\n                    \"location\": \"Lagos, Nigeria\",\n                    \"is_deleted\": false,\n                    \"createdAt\": \"2025-07-23T17:15:17.261Z\",\n                    \"updatedAt\": \"2025-07-23T17:15:17.261Z\"\n                },\n                \"placement\": {\n                    \"id\": 1,\n                    \"name\": \"Advert\",\n                    \"is_deleted\": false,\n                    \"createdAt\": \"2025-07-25T10:36:15.025Z\",\n                    \"updatedAt\": \"2025-07-25T10:36:15.025Z\"\n                },\n                \"title\": \"Editorial Title Example\",\n                \"page_number\": 12,\n                \"link\": \"https://example.com/editorial-article\",\n                \"reporter\": \"Jane Doe\",\n                \"country\": \"Nigeria\",\n                \"spokesperson\": \"John Smith\",\n                \"activity\": {\n                    \"id\": 1,\n                    \"name\": \"Money\",\n                    \"is_deleted\": false,\n                    \"createdAt\": \"2025-07-25T10:38:29.430Z\",\n                    \"updatedAt\": \"2025-07-25T10:38:29.430Z\"\n                },\n                \"sentiment\": \"positive\",\n                \"media_sentiment_index\": 3,\n                \"advert_spend\": 100000,\n                \"circulation\": 50000,\n                \"page_size\": \"Full Page\"\n            }\n        ],\n        \"meta\": {\n            \"total\": 2,\n            \"currentPage\": 1,\n            \"totalPage\": 1,\n            \"pageSize\": 10\n        },\n        \"links\": {\n            \"first\": \"http://localhost:3000/editorials?page=1\",\n            \"last\": \"http://localhost:3000/editorials?page=1\",\n            \"prev\": \"http://localhost:3000/editorials?page=1\",\n            \"next\": \"http://localhost:3000/editorials?pages=1\"\n        }\n    },\n    \"message\": \"All Editorials\"\n}"}]}, {"name": "createEditorial", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"date\": \"2025-07-25T00:00:00Z\",\r\n  \"media_type\": 1,\r\n  \"company\": 2,\r\n  \"brand\": \"BrandX\",\r\n  \"industry\": \"Technology\",\r\n  \"sub_sector\": \"Software\",\r\n  \"publication\": 3,\r\n  \"placement\": 1,\r\n  \"title\": \"Editorial Title Example\",\r\n  \"page_number\": 12,\r\n  \"link\": \"https://example.com/editorial-article\",\r\n  \"reporter\": \"<PERSON>\",\r\n  \"country\": \"Nigeria\",\r\n  \"spokesperson\": \"<PERSON>\",\r\n  \"activity\": 1,\r\n  \"sentiment\": \"positive\",\r\n  \"media_sentiment_index\": 3,\r\n  \"advert_spend\": 100000,\r\n  \"circulation\": 50000,\r\n  \"page_size\": \"Full Page\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/editorials/create", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "editorials", "create"]}}, "response": [{"name": "create", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"date\": \"2025-07-25T00:00:00Z\",\r\n  \"media_type\": 1,\r\n  \"company\": 2,\r\n  \"brand\": \"BrandX\",\r\n  \"industry\": \"Technology\",\r\n  \"sub_sector\": \"Software\",\r\n  \"publication\": 3,\r\n  \"placement\": 1,\r\n  \"title\": \"Editorial Title Example\",\r\n  \"page_number\": 12,\r\n  \"link\": \"https://example.com/editorial-article\",\r\n  \"reporter\": \"<PERSON>\",\r\n  \"country\": \"Nigeria\",\r\n  \"spokesperson\": \"<PERSON>\",\r\n  \"activity\": 1,\r\n  \"sentiment\": \"positive\",\r\n  \"media_sentiment_index\": 3,\r\n  \"advert_spend\": 100000,\r\n  \"circulation\": 50000,\r\n  \"page_size\": \"Full Page\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/editorials/create", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "editorials", "create"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "56"}, {"key": "ETag", "value": "W/\"38-gNzmSS4rSg96QQnpnbMGmwfRyOI\""}, {"key": "Date", "value": "Fri, 25 Jul 2025 10:53:16 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {},\n    \"message\": \"Editorial Created\"\n}"}]}, {"name": "getById", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/editorials/1", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "editorials", "1"]}}, "response": [{"name": "getById", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/editorials/1", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "editorials", "1"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "1797"}, {"key": "ETag", "value": "W/\"705-kpNWWVFmH7HlPCzJo9b/m7lJ804\""}, {"key": "Date", "value": "Fri, 25 Jul 2025 10:54:36 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"id\": 1,\n        \"date\": \"2025-07-25T00:00:00.000Z\",\n        \"media_type\": {\n            \"id\": 1,\n            \"name\": \"Print\",\n            \"is_deleted\": false,\n            \"createdAt\": \"2025-07-25T10:31:48.991Z\",\n            \"updatedAt\": \"2025-07-25T10:31:48.991Z\"\n        },\n        \"company\": {\n            \"id\": 2,\n            \"name\": \"MTN Nigeria\",\n            \"industry\": \"Telecommunications\",\n            \"sub_industry\": \"Mobile Network Operator\",\n            \"address\": \"Churchgate Tower, 30 Afribank Street, Victoria Island\",\n            \"state\": \"Lagos\",\n            \"country\": \"Nigeria\",\n            \"email\": \"<EMAIL>\",\n            \"contact\": \"<PERSON>\",\n            \"ceo\": \"<PERSON>\",\n            \"phone_no\": \"+234-************\",\n            \"website\": \"https://www.mtn.ng\",\n            \"facebook_link\": \"https://facebook.com/MTNng\",\n            \"instagram_link\": \"https://instagram.com/mtnng\",\n            \"twitter_link\": \"https://twitter.com/mtnng\",\n            \"linkedin_link\": \"https://linkedin.com/company/mtn-nigeria\",\n            \"youtube_link\": null,\n            \"is_deleted\": false,\n            \"createdAt\": \"2025-07-23T17:15:17.251Z\",\n            \"updatedAt\": \"2025-07-23T17:15:17.251Z\"\n        },\n        \"brand\": \"BrandX\",\n        \"industry\": \"Technology\",\n        \"sub_sector\": \"Software\",\n        \"publication\": {\n            \"id\": 3,\n            \"name\": \"ThisDay\",\n            \"type\": \"Newspaper\",\n            \"website\": \"https://thisdaylive.com\",\n            \"description\": \"Nigerian daily newspaper\",\n            \"circulation\": 100000,\n            \"reach\": 1500000,\n            \"location\": \"Lagos, Nigeria\",\n            \"is_deleted\": false,\n            \"createdAt\": \"2025-07-23T17:15:17.261Z\",\n            \"updatedAt\": \"2025-07-23T17:15:17.261Z\"\n        },\n        \"placement\": {\n            \"id\": 1,\n            \"name\": \"Advert\",\n            \"is_deleted\": false,\n            \"createdAt\": \"2025-07-25T10:36:15.025Z\",\n            \"updatedAt\": \"2025-07-25T10:36:15.025Z\"\n        },\n        \"title\": \"Editorial Title Example\",\n        \"page_number\": 12,\n        \"link\": \"https://example.com/editorial-article\",\n        \"reporter\": \"Jane Doe\",\n        \"country\": \"Nigeria\",\n        \"spokesperson\": \"John Smith\",\n        \"activity\": {\n            \"id\": 1,\n            \"name\": \"Money\",\n            \"is_deleted\": false,\n            \"createdAt\": \"2025-07-25T10:38:29.430Z\",\n            \"updatedAt\": \"2025-07-25T10:38:29.430Z\"\n        },\n        \"sentiment\": \"positive\",\n        \"media_sentiment_index\": 3,\n        \"advert_spend\": 100000,\n        \"circulation\": 50000,\n        \"page_size\": \"Full Page\"\n    },\n    \"message\": \"Single Editorial\"\n}"}]}, {"name": "update", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"activity\": \"1\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/editorials/update/2", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "editorials", "update", "2"]}}, "response": [{"name": "update", "originalRequest": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"activity\": \"1\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/editorials/update/2", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "editorials", "update", "2"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "61"}, {"key": "ETag", "value": "W/\"3d-+pOZi4PTLY0Xbps3a7xyZSCOOPI\""}, {"key": "Date", "value": "Fri, 25 Jul 2025 10:57:46 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": false,\n    \"message\": \"editorial not found\",\n    \"data\": null\n}"}]}, {"name": "delete", "request": {"method": "PUT", "header": [], "url": {"raw": "http://localhost:3000/api/editorials/delete/2", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "editorials", "delete", "2"]}}, "response": [{"name": "delete", "originalRequest": {"method": "PUT", "header": [], "url": {"raw": "http://localhost:3000/api/editorials/delete/2", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "editorials", "delete", "2"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "56"}, {"key": "ETag", "value": "W/\"38-NBQfpJGzP06PB2FLDY0y8daONX8\""}, {"key": "Date", "value": "Fri, 25 Jul 2025 10:57:23 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {},\n    \"message\": \"editorial Deleted\"\n}"}]}]}, {"name": "MediaTypes", "item": [{"name": "GetAll", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/media-types/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "media-types", ""]}}, "response": [{"name": "GetAll", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/media-types/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "media-types", ""]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "337"}, {"key": "ETag", "value": "W/\"151-coecq7GStlNDXQDJ6ke8ykAXBmU\""}, {"key": "Date", "value": "Fri, 25 Jul 2025 10:32:02 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"type\": [\n            {\n                \"id\": 1,\n                \"name\": \"Print\"\n            }\n        ],\n        \"meta\": {\n            \"total\": 1,\n            \"currentPage\": 1,\n            \"totalPage\": 1,\n            \"pageSize\": 10\n        },\n        \"links\": {\n            \"first\": \"http://localhost:3000/types?page=1\",\n            \"last\": \"http://localhost:3000/types?page=1\",\n            \"prev\": \"http://localhost:3000/types?page=1\",\n            \"next\": \"http://localhost:3000/types?pages=1\"\n        }\n    },\n    \"message\": \"All Media Types\"\n}"}]}, {"name": "createMEdiaType", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"Online\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/media-types/create", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "media-types", "create"]}}, "response": [{"name": "createMediaType", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"Online\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/media-types/create", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "media-types", "create"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "57"}, {"key": "ETag", "value": "W/\"39-mwkwhEpaeamXS4wObp9HpG4RrtM\""}, {"key": "Date", "value": "Fri, 25 Jul 2025 10:32:28 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {},\n    \"message\": \"Media Type Created\"\n}"}]}]}, {"name": "Placement", "item": [{"name": "GetAll", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/placements/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "placements", ""]}}, "response": [{"name": "getAll", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/placements/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "placements", ""]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "387"}, {"key": "ETag", "value": "W/\"183-lQz9Y7tkmXokOzL/vDZqv2zS12M\""}, {"key": "Date", "value": "Fri, 25 Jul 2025 10:36:38 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"placement\": [\n            {\n                \"id\": 1,\n                \"name\": \"Advert\"\n            },\n            {\n                \"id\": 2,\n                \"name\": \"<PERSON><PERSON>\"\n            }\n        ],\n        \"meta\": {\n            \"total\": 2,\n            \"currentPage\": 1,\n            \"totalPage\": 1,\n            \"pageSize\": 10\n        },\n        \"links\": {\n            \"first\": \"http://localhost:3000/placements?page=1\",\n            \"last\": \"http://localhost:3000/placements?page=1\",\n            \"prev\": \"http://localhost:3000/placements?page=1\",\n            \"next\": \"http://localhost:3000/placements?pages=1\"\n        }\n    },\n    \"message\": \"All Placements\"\n}"}]}, {"name": "createPlacement", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"<PERSON><PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/placements/create", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "placements", "create"]}}, "response": [{"name": "create", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"<PERSON><PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/placements/create", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "placements", "create"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "56"}, {"key": "ETag", "value": "W/\"38-Co7mq5LyxLD+aTm6FbMpuMqDsBg\""}, {"key": "Date", "value": "Fri, 25 Jul 2025 10:36:29 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {},\n    \"message\": \"Placement Created\"\n}"}]}]}, {"name": "Export", "item": [{"name": "ExportUsers", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"activity\": \"1\"\r\n}"}, "url": {"raw": "http://localhost:3000/api/export/users?format=csv", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "export", "users"], "query": [{"key": "format", "value": "csv"}]}}, "response": [{"name": "exportUsers", "originalRequest": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"activity\": \"1\"\r\n}"}, "url": {"raw": "http://localhost:3000/api/export/users?format=csv", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "export", "users"], "query": [{"key": "format", "value": "csv"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "plain", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "text/csv; charset=utf-8"}, {"key": "Content-Disposition", "value": "attachment; filename=\"users_2025-07-25.csv\""}, {"key": "Content-Length", "value": "386"}, {"key": "ETag", "value": "W/\"182-NU1sIHKcIqw6b5CHqx1KZB/wKoY\""}, {"key": "Date", "value": "Fri, 25 Jul 2025 12:53:40 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "id,username,email,phone,gender,active,role.name\n1,admin,<EMAIL>,+2348012345678,male,true,Company\n2,supervisor,<EMAIL>,+2348012345679,female,true,Supervisor\n4,analyst2,<EMAIL>,+2348012345681,female,true,Analyst\n3,analyst1,<EMAIL>,+2348012345680,male,true,Analyst\n5,client1,<EMAIL>,+2348012345682,male,true,Client"}]}, {"name": "ExportCompanies", "event": [{"listen": "test", "script": {"exec": ["\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/export/companies?format=csv", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "export", "companies"], "query": [{"key": "format", "value": "csv"}]}}, "response": [{"name": "exportCompanies", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/export/companies?format=csv", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "export", "companies"], "query": [{"key": "format", "value": "csv"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "plain", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "text/csv; charset=utf-8"}, {"key": "Content-Disposition", "value": "attachment; filename=\"companies_2025-07-25.csv\""}, {"key": "Content-Length", "value": "729"}, {"key": "ETag", "value": "W/\"2d9-YS0Gl975/Vm2nk3XRgBs9kggchI\""}, {"key": "Date", "value": "Fri, 25 Jul 2025 13:06:01 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "id,name,industry,sub_industry,address,state,country,email,phone_no,website\n1,Dangote Group,Conglomerate,\"Cement, Sugar, Oil & Gas\",\"1 Alfred Rewane Road, Ikoyi\",Lagos,Nigeria,<EMAIL>,+234-1-448-0815,https://www.dangote.com\n3,Guaranty Trust Bank,Financial Services,Commercial Banking,\"635 Akin Adesola Street, Victoria Island\",Lagos,Nigeria,<EMAIL>,+234-1-448-5500,https://www.gtbank.com\n2,MTN Nigeria,Telecommunications,Mobile Network Operator,\"Churchgate Tower, 30 Afribank Street, Victoria Island\",Lagos,Nigeria,<EMAIL>,+234-************,https://www.mtn.ng\n5,Nigerian Breweries,Consumer Goods,Beverages,\"Iganmu Industrial Estate, Surulere\",Lagos,Nigeria,<EMAIL>,+234-1-270-2000,https://www.nbplc.com"}]}, {"name": "ExportPublications", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/export/publications?format=csv", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "export", "publications"], "query": [{"key": "format", "value": "csv"}]}}, "response": [{"name": "exportPublications", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/export/publications?format=csv", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "export", "publications"], "query": [{"key": "format", "value": "csv"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "plain", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "text/csv; charset=utf-8"}, {"key": "Content-Disposition", "value": "attachment; filename=\"publications_2025-07-25.csv\""}, {"key": "Content-Length", "value": "869"}, {"key": "ETag", "value": "W/\"365-6O3haN/ryJeaQegI7jvJOEOAWFg\""}, {"key": "Date", "value": "Fri, 25 Jul 2025 13:07:41 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "id,name,type,website,description,circulation,reach,location\n4,Channels TV,Television,https://channelstv.com,Nigerian television station,,5000000,\"Lagos, Nigeria\"\n6,Nairametrics,Online,https://nairametrics.com,Nigerian business and financial news,,600000,\"Lagos, Nigeria\"\n7,Premium Times,Online,https://premiumtimesng.com,Nigerian online newspaper,,1200000,\"Abuja, Nigeria\"\n5,TechCabal,Online,https://techcabal.com,African technology news platform,,800000,\"Lagos, Nigeria\"\n8,The Cable,Online,https://thecable.ng,Nigerian online news platform,,900000,\"Lagos, Nigeria\"\n1,The Punch,Newspaper,https://punchng.com,Leading Nigerian newspaper,150000,2000000,\"Lagos, Nigeria\"\n3,ThisDay,Newspaper,https://thisdaylive.com,Nigerian daily newspaper,100000,1500000,\"Lagos, Nigeria\"\n2,Vanguard,Newspaper,https://vanguardngr.com,Nigerian daily newspaper,120000,1800000,\"Lagos, Nigeria\""}]}, {"name": "ExportEditorials", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/export/editorials?format=csv", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "export", "editorials"], "query": [{"key": "format", "value": "csv"}]}}, "response": [{"name": "exportEditorials", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/export/editorials?format=csv", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "export", "editorials"], "query": [{"key": "format", "value": "csv"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "plain", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "text/csv; charset=utf-8"}, {"key": "Content-Disposition", "value": "attachment; filename=\"editorials_2025-07-25.csv\""}, {"key": "Content-Length", "value": "183"}, {"key": "ETag", "value": "W/\"b7-PJ5Fwx5KDC6JygOOEZJx1CWZA0U\""}, {"key": "Date", "value": "Fri, 25 Jul 2025 13:19:43 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "id,title,content,status,company_data.name,publication_data.name,createdAt\n1,Editorial Title Example,,,MTN Nigeria,ThisDay,Fri Jul 25 2025 11:39:29 GMT+0100 (West Africa Standard Time)"}]}, {"name": "ExportDailyMentions", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/export/daily-mentions?format=csv", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "export", "daily-mentions"], "query": [{"key": "format", "value": "csv"}]}}, "response": [{"name": "exportDailyMentions", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/export/daily-mentions?format=csv", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "export", "daily-mentions"], "query": [{"key": "format", "value": "csv"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "plain", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "text/csv; charset=utf-8"}, {"key": "Content-Disposition", "value": "attachment; filename=\"daily_mentions_2025-07-25.csv\""}, {"key": "Content-Length", "value": "2639"}, {"key": "ETag", "value": "W/\"a4f-pvwjioBt8/VMsUWi4mPoTIf+/TY\""}, {"key": "Date", "value": "Fri, 25 Jul 2025 13:21:29 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "id,headline,sentiment,sentiment_score,media_type,reach,engagement,date,company.name,publication.name\n18,<PERSON>ple Headline2,positive,0.85,online,10000,500,Thu Jul 24 2025 01:00:00 GMT+0100 (West Africa Standard Time),Dangote Group,Vanguard\n9,Sample Headline,positive,0.85,online,10000,500,Thu Jul 24 2025 01:00:00 GMT+0100 (West Africa Standard Time),Dangote Group,Vanguard\n10,Sample Headline,positive,0.85,online,10000,500,Thu Jul 24 2025 01:00:00 GMT+0100 (West Africa Standard Time),Dangote Group,Vanguard\n12,Sample Headline,positive,0.85,online,10000,500,Thu Jul 24 2025 01:00:00 GMT+0100 (West Africa Standard Time),Dangote Group,Vanguard\n13,Sample Headline,neutral,0.85,online,10000,500,Thu Jul 24 2025 01:00:00 GMT+0100 (West Africa Standard Time),Dangote Group,Vanguard\n14,Sample Headline,positive,,online,10000,500,Thu Jul 24 2025 01:00:00 GMT+0100 (West Africa Standard Time),Dangote Group,Vanguard\n15,<PERSON><PERSON> Headline,positive,0.85,online,0,500,Thu Jul 24 2025 01:00:00 GMT+0100 (West Africa Standard Time),Dangote Group,Vanguard\n16,Sample Headline,positive,0.85,online,10000,,Thu Jul 24 2025 01:00:00 GMT+0100 (West Africa Standard Time),Dangote Group,Vanguard\n17,Sample Headline,positive,0.85,online,10000,500,Thu Jul 24 2025 01:00:00 GMT+0100 (West Africa Standard Time),Dangote Group,Vanguard\n8,GTBank Wins Best Digital Bank Award,positive,0.90,print,450000,1100,Mon Jan 22 2024 01:00:00 GMT+0100 (West Africa Standard Time),Guaranty Trust Bank,ThisDay\n7,MTN Nigeria Reports Data Revenue Growth,positive,0.80,online,350000,700,Sun Jan 21 2024 01:00:00 GMT+0100 (West Africa Standard Time),MTN Nigeria,The Cable\n6,Dangote Refinery Delays Production Start Date,negative,-0.40,online,600000,1500,Sat Jan 20 2024 01:00:00 GMT+0100 (West Africa Standard Time),Dangote Group,Premium Times\n5,Nigerian Breweries Commits to Sustainability Goals,positive,0.60,print,400000,900,Fri Jan 19 2024 01:00:00 GMT+0100 (West Africa Standard Time),Nigerian Breweries,Vanguard\n4,Shoprite Introduces AI-Powered Shopping Experience,positive,0.70,online,150000,600,Thu Jan 18 2024 01:00:00 GMT+0100 (West Africa Standard Time),,TechCabal\n3,GTBank Digital Banking Platform Experiences Downtime,negative,-0.60,online,300000,800,Wed Jan 17 2024 01:00:00 GMT+0100 (West Africa Standard Time),Guaranty Trust Bank,Nairametrics\n2,MTN Nigeria Launches 5G Network in Lagos,positive,0.90,broadcast,2000000,5000,Tue Jan 16 2024 01:00:00 GMT+0100 (West Africa Standard Time),MTN Nigeria,Channels TV\n1,Dangote Cement Reports Strong Q4 Performance,positive,0.80,print,500000,1200,Mon Jan 15 2024 01:00:00 GMT+0100 (West Africa Standard Time),Dangote Group,The Punch"}]}, {"name": "ExportSwotAnalysis", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/export/swot-analysis?format=csv", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "export", "swot-analysis"], "query": [{"key": "format", "value": "csv"}]}}, "response": [{"name": "exportSwotAnalysis", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/export/swot-analysis?format=csv", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "export", "swot-analysis"], "query": [{"key": "format", "value": "csv"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "html", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "text/html; charset=utf-8"}, {"key": "Content-Length", "value": "17"}, {"key": "ETag", "value": "W/\"11-yrilCKDadBQoTQyI8nVk7HTGmfs\""}, {"key": "Date", "value": "Fri, 25 Jul 2025 13:23:17 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "No data to export"}]}, {"name": "ExportAnalytics", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/export/analytics?format=json", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "export", "analytics"], "query": [{"key": "format", "value": "json"}]}}, "response": [{"name": "exportAnalytics", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/export/analytics?format=json", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "export", "analytics"], "query": [{"key": "format", "value": "json"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "241"}, {"key": "ETag", "value": "W/\"f1-1+06x73q4Ve2VYNjeVsZQg23LRw\""}, {"key": "Date", "value": "Fri, 25 Jul 2025 13:26:23 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"summary\": {\n            \"totalMentions\": 17,\n            \"totalSwotAnalyses\": 0,\n            \"totalOutcomeInsights\": 0,\n            \"totalCompanies\": 4,\n            \"totalUsers\": 5\n        },\n        \"exportDate\": \"2025-07-25T13:26:23.339Z\",\n        \"dateRange\": {}\n    },\n    \"message\": \"Analytics data exported successfully\"\n}"}]}]}, {"name": "Files", "item": [{"name": "getAll", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/files/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "files", ""]}}, "response": [{"name": "getAllFiles", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/files/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "files", ""]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "4433"}, {"key": "ETag", "value": "W/\"1151-8RXlQcS6UKFNxvp8AceXO501nIM\""}, {"key": "Date", "value": "Fri, 25 Jul 2025 17:32:04 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"data\": [\n            {\n                \"id\": 8,\n                \"filename\": \"files-1753464677212-832399172.pdf\",\n                \"original_name\": \"CamScanner 06-05-2024 23.00.pdf\",\n                \"file_path\": \"uploads\\\\files-1753464677212-832399172.pdf\",\n                \"file_size\": \"542972\",\n                \"mime_type\": \"application/pdf\",\n                \"file_type\": \"document\",\n                \"uploaded_by\": 1,\n                \"description\": \"New\",\n                \"tags\": {\n                    \"Project\": \"2025\"\n                },\n                \"metadata\": {},\n                \"status\": \"active\",\n                \"access_level\": \"private\",\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-25T17:31:17.230Z\",\n                \"updatedAt\": \"2025-07-25T17:31:17.230Z\",\n                \"uploader\": {\n                    \"id\": 1,\n                    \"username\": \"admin\",\n                    \"email\": \"<EMAIL>\"\n                }\n            },\n            {\n                \"id\": 7,\n                \"filename\": \"files-1753464677175-908188559.pdf\",\n                \"original_name\": \"Africa has too many businesses, too little busine….pdf\",\n                \"file_path\": \"uploads\\\\files-1753464677175-908188559.pdf\",\n                \"file_size\": \"4210329\",\n                \"mime_type\": \"application/pdf\",\n                \"file_type\": \"document\",\n                \"uploaded_by\": 1,\n                \"description\": \"New\",\n                \"tags\": {\n                    \"Project\": \"2025\"\n                },\n                \"metadata\": {},\n                \"status\": \"active\",\n                \"access_level\": \"private\",\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-25T17:31:17.224Z\",\n                \"updatedAt\": \"2025-07-25T17:31:17.224Z\",\n                \"uploader\": {\n                    \"id\": 1,\n                    \"username\": \"admin\",\n                    \"email\": \"<EMAIL>\"\n                }\n            },\n            {\n                \"id\": 6,\n                \"filename\": \"files-1753464626152-403135502.pdf\",\n                \"original_name\": \"CamScanner 06-05-2024 23.00.pdf\",\n                \"file_path\": \"uploads\\\\files-1753464626152-403135502.pdf\",\n                \"file_size\": \"542972\",\n                \"mime_type\": \"application/pdf\",\n                \"file_type\": \"document\",\n                \"uploaded_by\": 1,\n                \"description\": \"\",\n                \"tags\": {\n                    \"Project\": \"2025\"\n                },\n                \"metadata\": {},\n                \"status\": \"active\",\n                \"access_level\": \"private\",\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-25T17:30:26.177Z\",\n                \"updatedAt\": \"2025-07-25T17:30:26.177Z\",\n                \"uploader\": {\n                    \"id\": 1,\n                    \"username\": \"admin\",\n                    \"email\": \"<EMAIL>\"\n                }\n            },\n            {\n                \"id\": 5,\n                \"filename\": \"files-1753464626092-251053898.pdf\",\n                \"original_name\": \"Africa has too many businesses, too little busine….pdf\",\n                \"file_path\": \"uploads\\\\files-1753464626092-251053898.pdf\",\n                \"file_size\": \"4210329\",\n                \"mime_type\": \"application/pdf\",\n                \"file_type\": \"document\",\n                \"uploaded_by\": 1,\n                \"description\": \"\",\n                \"tags\": {\n                    \"Project\": \"2025\"\n                },\n                \"metadata\": {},\n                \"status\": \"active\",\n                \"access_level\": \"private\",\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-25T17:30:26.161Z\",\n                \"updatedAt\": \"2025-07-25T17:30:26.161Z\",\n                \"uploader\": {\n                    \"id\": 1,\n                    \"username\": \"admin\",\n                    \"email\": \"<EMAIL>\"\n                }\n            },\n            {\n                \"id\": 4,\n                \"filename\": \"file-1753464172019-722718494.pdf\",\n                \"original_name\": \"Africa has too many businesses, too little busine….pdf\",\n                \"file_path\": \"uploads\\\\file-1753464172019-722718494.pdf\",\n                \"file_size\": \"4210329\",\n                \"mime_type\": \"application/pdf\",\n                \"file_type\": \"document\",\n                \"uploaded_by\": 1,\n                \"description\": \"\",\n                \"tags\": {\n                    \"project\": \"2025\"\n                },\n                \"metadata\": {},\n                \"status\": \"active\",\n                \"access_level\": \"private\",\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-25T17:22:52.059Z\",\n                \"updatedAt\": \"2025-07-25T17:22:52.059Z\",\n                \"uploader\": {\n                    \"id\": 1,\n                    \"username\": \"admin\",\n                    \"email\": \"<EMAIL>\"\n                }\n            },\n            {\n                \"id\": 3,\n                \"filename\": \"file-1753464165845-686557391.pdf\",\n                \"original_name\": \"Africa has too many businesses, too little busine….pdf\",\n                \"file_path\": \"uploads\\\\file-1753464165845-686557391.pdf\",\n                \"file_size\": \"4210329\",\n                \"mime_type\": \"application/pdf\",\n                \"file_type\": \"document\",\n                \"uploaded_by\": 1,\n                \"description\": \"\",\n                \"tags\": [],\n                \"metadata\": {},\n                \"status\": \"active\",\n                \"access_level\": \"private\",\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-25T17:22:45.887Z\",\n                \"updatedAt\": \"2025-07-25T17:22:45.887Z\",\n                \"uploader\": {\n                    \"id\": 1,\n                    \"username\": \"admin\",\n                    \"email\": \"<EMAIL>\"\n                }\n            },\n            {\n                \"id\": 2,\n                \"filename\": \"file-1753464155690-198364167.pdf\",\n                \"original_name\": \"Africa has too many businesses, too little busine….pdf\",\n                \"file_path\": \"uploads\\\\file-1753464155690-198364167.pdf\",\n                \"file_size\": \"4210329\",\n                \"mime_type\": \"application/pdf\",\n                \"file_type\": \"document\",\n                \"uploaded_by\": 1,\n                \"description\": \"\",\n                \"tags\": {\n                    \"project\": \"2025\"\n                },\n                \"metadata\": {},\n                \"status\": \"active\",\n                \"access_level\": \"private\",\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-25T17:22:35.747Z\",\n                \"updatedAt\": \"2025-07-25T17:22:35.747Z\",\n                \"uploader\": {\n                    \"id\": 1,\n                    \"username\": \"admin\",\n                    \"email\": \"<EMAIL>\"\n                }\n            },\n            {\n                \"id\": 1,\n                \"filename\": \"file-1753463985059-840732936.pdf\",\n                \"original_name\": \"Africa has too many businesses, too little busine….pdf\",\n                \"file_path\": \"uploads\\\\file-1753463985059-840732936.pdf\",\n                \"file_size\": \"4210329\",\n                \"mime_type\": \"application/pdf\",\n                \"file_type\": \"document\",\n                \"uploaded_by\": 1,\n                \"description\": \"\",\n                \"tags\": {\n                    \"project\": \"2025\"\n                },\n                \"metadata\": {},\n                \"status\": \"active\",\n                \"access_level\": \"private\",\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-25T17:19:45.116Z\",\n                \"updatedAt\": \"2025-07-25T17:19:45.116Z\",\n                \"uploader\": {\n                    \"id\": 1,\n                    \"username\": \"admin\",\n                    \"email\": \"<EMAIL>\"\n                }\n            }\n        ],\n        \"pagination\": {\n            \"total\": 8,\n            \"page\": 1,\n            \"limit\": 10,\n            \"totalPages\": 1\n        }\n    },\n    \"message\": \"Files retrieved successfully\"\n}"}]}, {"name": "uploadFile", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsImlhdCI6MTc1MzQ2MzgyNCwiZXhwIjoxNzUzNTUwMjI0fQ.v1jgi_Sh7ek45_4fm875YytxbbuzHxJTEvduKOv9-f4", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/C:/Users/<USER>/Downloads/Africa has too many businesses, too little busine….pdf"}, {"key": "description ", "value": "New file", "type": "text"}, {"key": "tags", "value": "{\"project\": \"2025\"}", "type": "text"}]}, "url": {"raw": "http://localhost:3000/api/files/upload", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "files", "upload"]}}, "response": [{"name": "uploadFile", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/C:/Users/<USER>/Downloads/Africa has too many businesses, too little busine….pdf"}, {"key": "description ", "value": "New file", "type": "text"}, {"key": "tags", "value": "{\"project\": \"2025\"}", "type": "text"}]}, "url": {"raw": "http://localhost:3000/api/files/upload", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "files", "upload"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "606"}, {"key": "ETag", "value": "W/\"25e-JQS4O8yWlX+u6HPx5XH1q09VVxk\""}, {"key": "Date", "value": "Fri, 25 Jul 2025 17:22:52 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"id\": 4,\n        \"filename\": \"file-1753464172019-722718494.pdf\",\n        \"original_name\": \"Africa has too many businesses, too little busine….pdf\",\n        \"file_path\": \"uploads\\\\file-1753464172019-722718494.pdf\",\n        \"file_size\": \"4210329\",\n        \"mime_type\": \"application/pdf\",\n        \"file_type\": \"document\",\n        \"uploaded_by\": 1,\n        \"description\": \"\",\n        \"tags\": {\n            \"project\": \"2025\"\n        },\n        \"metadata\": {},\n        \"status\": \"active\",\n        \"access_level\": \"private\",\n        \"is_deleted\": false,\n        \"createdAt\": \"2025-07-25T17:22:52.059Z\",\n        \"updatedAt\": \"2025-07-25T17:22:52.059Z\",\n        \"uploader\": {\n            \"id\": 1,\n            \"username\": \"admin\",\n            \"email\": \"<EMAIL>\"\n        }\n    },\n    \"message\": \"File uploaded successfully\"\n}"}]}, {"name": "uploadMultipleFiles", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsImlhdCI6MTc1MzQ2MzgyNCwiZXhwIjoxNzUzNTUwMjI0fQ.v1jgi_Sh7ek45_4fm875YytxbbuzHxJTEvduKOv9-f4", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "files", "type": "file", "src": ["postman-cloud:///1f01bfc7-eaea-4a50-bcab-2539e4061865", "postman-cloud:///1f01bffe-2aaa-4510-ade0-90486651c007"]}, {"key": "description", "value": "New", "type": "text"}, {"key": "tags", "value": "{\"Project\":\"2025\"}", "type": "text"}]}, "url": {"raw": "http://localhost:3000/api/files/upload-multiple", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "files", "upload-multiple"]}}, "response": [{"name": "uploadMultipleFiles", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "files", "type": "file", "src": ["postman-cloud:///1f01bfc7-eaea-4a50-bcab-2539e4061865", "postman-cloud:///1f01bffe-2aaa-4510-ade0-90486651c007"]}, {"key": "description", "value": "New", "type": "text"}, {"key": "tags", "value": "{\"Project\":\"2025\"}", "type": "text"}]}, "url": {"raw": "http://localhost:3000/api/files/upload-multiple", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "files", "upload-multiple"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "997"}, {"key": "ETag", "value": "W/\"3e5-oPhYXKFD0SXYVYRF5m5Ok5UCxWc\""}, {"key": "Date", "value": "Fri, 25 Jul 2025 17:31:17 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": [\n        {\n            \"metadata\": {},\n            \"status\": \"active\",\n            \"access_level\": \"private\",\n            \"is_deleted\": false,\n            \"id\": 7,\n            \"filename\": \"files-1753464677175-908188559.pdf\",\n            \"original_name\": \"Africa has too many businesses, too little busine….pdf\",\n            \"file_path\": \"uploads\\\\files-1753464677175-908188559.pdf\",\n            \"file_size\": \"4210329\",\n            \"mime_type\": \"application/pdf\",\n            \"file_type\": \"document\",\n            \"uploaded_by\": 1,\n            \"description\": \"New\",\n            \"tags\": {\n                \"Project\": \"2025\"\n            },\n            \"updatedAt\": \"2025-07-25T17:31:17.224Z\",\n            \"createdAt\": \"2025-07-25T17:31:17.224Z\"\n        },\n        {\n            \"metadata\": {},\n            \"status\": \"active\",\n            \"access_level\": \"private\",\n            \"is_deleted\": false,\n            \"id\": 8,\n            \"filename\": \"files-1753464677212-832399172.pdf\",\n            \"original_name\": \"CamScanner 06-05-2024 23.00.pdf\",\n            \"file_path\": \"uploads\\\\files-1753464677212-832399172.pdf\",\n            \"file_size\": \"542972\",\n            \"mime_type\": \"application/pdf\",\n            \"file_type\": \"document\",\n            \"uploaded_by\": 1,\n            \"description\": \"New\",\n            \"tags\": {\n                \"Project\": \"2025\"\n            },\n            \"updatedAt\": \"2025-07-25T17:31:17.230Z\",\n            \"createdAt\": \"2025-07-25T17:31:17.230Z\"\n        }\n    ],\n    \"message\": \"Files uploaded successfully\"\n}"}]}, {"name": "getById", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsImlhdCI6MTc1MzQ2MzgyNCwiZXhwIjoxNzUzNTUwMjI0fQ.v1jgi_Sh7ek45_4fm875YytxbbuzHxJTEvduKOv9-f4", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "files", "type": "file", "src": ["postman-cloud:///1f01bfc7-eaea-4a50-bcab-2539e4061865", "postman-cloud:///1f01bffe-2aaa-4510-ade0-90486651c007"]}, {"key": "description", "value": "New", "type": "text"}, {"key": "tags", "value": "{\"Project\":\"2025\"}", "type": "text"}]}, "url": {"raw": "http://localhost:3000/api/files/1", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "files", "1"]}}, "response": [{"name": "getById", "originalRequest": {"method": "GET", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "files", "type": "file", "src": ["postman-cloud:///1f01bfc7-eaea-4a50-bcab-2539e4061865", "postman-cloud:///1f01bffe-2aaa-4510-ade0-90486651c007"]}, {"key": "description", "value": "New", "type": "text"}, {"key": "tags", "value": "{\"Project\":\"2025\"}", "type": "text"}]}, "url": {"raw": "http://localhost:3000/api/files/1", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "files", "1"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "607"}, {"key": "ETag", "value": "W/\"25f-rFWG0wiXYpdYuZaH/RkNoGFRDww\""}, {"key": "Date", "value": "Fri, 25 Jul 2025 17:40:05 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"id\": 1,\n        \"filename\": \"file-1753463985059-840732936.pdf\",\n        \"original_name\": \"Africa has too many businesses, too little busine….pdf\",\n        \"file_path\": \"uploads\\\\file-1753463985059-840732936.pdf\",\n        \"file_size\": \"4210329\",\n        \"mime_type\": \"application/pdf\",\n        \"file_type\": \"document\",\n        \"uploaded_by\": 1,\n        \"description\": \"\",\n        \"tags\": {\n            \"project\": \"2025\"\n        },\n        \"metadata\": {},\n        \"status\": \"active\",\n        \"access_level\": \"private\",\n        \"is_deleted\": false,\n        \"createdAt\": \"2025-07-25T17:19:45.116Z\",\n        \"updatedAt\": \"2025-07-25T17:19:45.116Z\",\n        \"uploader\": {\n            \"id\": 1,\n            \"username\": \"admin\",\n            \"email\": \"<EMAIL>\"\n        }\n    },\n    \"message\": \"File retrieved successfully\"\n}"}]}, {"name": "delete", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsImlhdCI6MTc1MzQ2MzgyNCwiZXhwIjoxNzUzNTUwMjI0fQ.v1jgi_Sh7ek45_4fm875YytxbbuzHxJTEvduKOv9-f4", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "files", "type": "file", "src": ["postman-cloud:///1f01bfc7-eaea-4a50-bcab-2539e4061865", "postman-cloud:///1f01bffe-2aaa-4510-ade0-90486651c007"]}, {"key": "description", "value": "New", "type": "text"}, {"key": "tags", "value": "{\"Project\":\"2025\"}", "type": "text"}]}, "url": {"raw": "http://localhost:3000/api/files/delete/8", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "files", "delete", "8"]}}, "response": [{"name": "delete", "originalRequest": {"method": "PUT", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "files", "type": "file", "src": ["postman-cloud:///1f01bfc7-eaea-4a50-bcab-2539e4061865", "postman-cloud:///1f01bffe-2aaa-4510-ade0-90486651c007"]}, {"key": "description", "value": "New", "type": "text"}, {"key": "tags", "value": "{\"Project\":\"2025\"}", "type": "text"}]}, "url": {"raw": "http://localhost:3000/api/files/delete/8", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "files", "delete", "8"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "64"}, {"key": "ETag", "value": "W/\"40-75Qyw4u5rhKuK2mKiegWWgY4Cv4\""}, {"key": "Date", "value": "Fri, 25 Jul 2025 17:43:06 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {},\n    \"message\": \"File deleted successfully\"\n}"}]}, {"name": "download", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsImlhdCI6MTc1MzQ2MzgyNCwiZXhwIjoxNzUzNTUwMjI0fQ.v1jgi_Sh7ek45_4fm875YytxbbuzHxJTEvduKOv9-f4", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "files", "type": "file", "src": ["postman-cloud:///1f01bfc7-eaea-4a50-bcab-2539e4061865", "postman-cloud:///1f01bffe-2aaa-4510-ade0-90486651c007"]}, {"key": "description", "value": "New", "type": "text"}, {"key": "tags", "value": "{\"Project\":\"2025\"}", "type": "text"}]}, "url": {"raw": "http://localhost:3000/api/files/7/download", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "files", "7", "download"]}}, "response": [{"name": "delete", "originalRequest": {"method": "PUT", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "files", "type": "file", "src": ["postman-cloud:///1f01bfc7-eaea-4a50-bcab-2539e4061865", "postman-cloud:///1f01bffe-2aaa-4510-ade0-90486651c007"]}, {"key": "description", "value": "New", "type": "text"}, {"key": "tags", "value": "{\"Project\":\"2025\"}", "type": "text"}]}, "url": {"raw": "http://localhost:3000/api/files/delete/8", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "files", "delete", "8"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "64"}, {"key": "ETag", "value": "W/\"40-75Qyw4u5rhKuK2mKiegWWgY4Cv4\""}, {"key": "Date", "value": "Fri, 25 Jul 2025 17:43:06 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {},\n    \"message\": \"File deleted successfully\"\n}"}]}]}, {"name": "MediaChannel", "item": [{"name": "GetAll", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsImlhdCI6MTc1MzQ2MzgyNCwiZXhwIjoxNzUzNTUwMjI0fQ.v1jgi_Sh7ek45_4fm875YytxbbuzHxJTEvduKOv9-f4", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "files", "type": "file", "src": ["postman-cloud:///1f01bfc7-eaea-4a50-bcab-2539e4061865", "postman-cloud:///1f01bffe-2aaa-4510-ade0-90486651c007"]}, {"key": "description", "value": "New", "type": "text"}, {"key": "tags", "value": "{\"Project\":\"2025\"}", "type": "text"}]}, "url": {"raw": "http://localhost:3000/api/media-channels/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "media-channels", ""]}}, "response": [{"name": "getAll", "originalRequest": {"method": "GET", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "files", "type": "file", "src": ["postman-cloud:///1f01bfc7-eaea-4a50-bcab-2539e4061865", "postman-cloud:///1f01bffe-2aaa-4510-ade0-90486651c007"]}, {"key": "description", "value": "New", "type": "text"}, {"key": "tags", "value": "{\"Project\":\"2025\"}", "type": "text"}]}, "url": {"raw": "http://localhost:3000/api/media-channels/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "media-channels", ""]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "1747"}, {"key": "ETag", "value": "W/\"6d3-LZaK79jkb69s9dLSNEhamSk3jLo\""}, {"key": "Date", "value": "Fri, 25 Jul 2025 21:23:05 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"data\": [\n            {\n                \"id\": 1,\n                \"name\": \"Example Channel\",\n                \"type\": \"online\",\n                \"category\": \"News\",\n                \"website\": \"https://example.com\",\n                \"description\": \"A sample media channel.\",\n                \"reach\": \"100000\",\n                \"audience_demographics\": {\n                    \"age\": \"18-35\",\n                    \"gender\": \"mixed\"\n                },\n                \"contact_info\": {\n                    \"email\": \"<EMAIL>\",\n                    \"phone\": \"+123456789\"\n                },\n                \"social_handles\": {\n                    \"twitter\": \"@example\"\n                },\n                \"location\": \"Lagos\",\n                \"language\": \"English\",\n                \"frequency\": \"Daily\",\n                \"status\": \"active\",\n                \"rating\": \"4.5\",\n                \"tags\": [\n                    \"news\",\n                    \"media\"\n                ],\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-25T21:17:34.852Z\",\n                \"updatedAt\": \"2025-07-25T21:17:34.852Z\"\n            },\n            {\n                \"id\": 3,\n                \"name\": \"Example Channel 2\",\n                \"type\": \"online\",\n                \"category\": \"News\",\n                \"website\": \"https://example.com\",\n                \"description\": \"A sample media channel.\",\n                \"reach\": \"100000\",\n                \"audience_demographics\": {\n                    \"age\": \"18-35\",\n                    \"gender\": \"mixed\"\n                },\n                \"contact_info\": {\n                    \"email\": \"<EMAIL>\",\n                    \"phone\": \"+123456789\"\n                },\n                \"social_handles\": {\n                    \"twitter\": \"@example\"\n                },\n                \"location\": \"Lagos\",\n                \"language\": \"English\",\n                \"frequency\": \"Daily\",\n                \"status\": \"active\",\n                \"rating\": \"4.5\",\n                \"tags\": [\n                    \"news\",\n                    \"media\"\n                ],\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-25T21:19:06.397Z\",\n                \"updatedAt\": \"2025-07-25T21:19:06.397Z\"\n            },\n            {\n                \"id\": 4,\n                \"name\": \"Example Channel 4\",\n                \"type\": \"online\",\n                \"category\": \"News\",\n                \"website\": \"https://example.com\",\n                \"description\": \"A sample media channel.\",\n                \"reach\": \"100000\",\n                \"audience_demographics\": {\n                    \"age\": \"18-35\",\n                    \"gender\": \"mixed\"\n                },\n                \"contact_info\": {\n                    \"email\": \"<EMAIL>\",\n                    \"phone\": \"+123456789\"\n                },\n                \"social_handles\": {\n                    \"twitter\": \"@example\"\n                },\n                \"location\": \"Lagos\",\n                \"language\": \"English\",\n                \"frequency\": \"Daily\",\n                \"status\": \"active\",\n                \"rating\": \"4.5\",\n                \"tags\": [\n                    \"news\",\n                    \"media\"\n                ],\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-25T21:22:54.645Z\",\n                \"updatedAt\": \"2025-07-25T21:22:54.645Z\"\n            }\n        ],\n        \"pagination\": {\n            \"total\": 3,\n            \"page\": 1,\n            \"limit\": 10,\n            \"totalPages\": 1\n        }\n    },\n    \"message\": \"Media channels retrieved successfully\"\n}"}]}, {"name": "create", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"Example Channel 4\",                \r\n  \"type\": \"online\",                      \r\n  \"category\": \"News\",                      \r\n  \"website\": \"https://example.com\",         \r\n  \"description\": \"A sample media channel.\", \r\n  \"reach\": 100000,                          \r\n  \"audience_demographics\": {              \r\n    \"age\": \"18-35\",\r\n    \"gender\": \"mixed\"\r\n  },\r\n  \"contact_info\": {                       \r\n    \"email\": \"<EMAIL>\",\r\n    \"phone\": \"+123456789\"\r\n  },\r\n  \"social_handles\": {                   \r\n    \"twitter\": \"@example\"\r\n  },\r\n  \"location\": \"Lagos\",                   \r\n  \"language\": \"English\",                    \r\n  \"frequency\": \"Daily\",                 \r\n  \"status\": \"active\",                       \r\n  \"rating\": 4.5,                        \r\n  \"tags\": [\"news\", \"media\"]                \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/media-channels/create", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "media-channels", "create"]}}, "response": [{"name": "create", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"Example Channel 4\",                \r\n  \"type\": \"online\",                      \r\n  \"category\": \"News\",                      \r\n  \"website\": \"https://example.com\",         \r\n  \"description\": \"A sample media channel.\", \r\n  \"reach\": 100000,                          \r\n  \"audience_demographics\": {              \r\n    \"age\": \"18-35\",\r\n    \"gender\": \"mixed\"\r\n  },\r\n  \"contact_info\": {                       \r\n    \"email\": \"<EMAIL>\",\r\n    \"phone\": \"+123456789\"\r\n  },\r\n  \"social_handles\": {                   \r\n    \"twitter\": \"@example\"\r\n  },\r\n  \"location\": \"Lagos\",                   \r\n  \"language\": \"English\",                    \r\n  \"frequency\": \"Daily\",                 \r\n  \"status\": \"active\",                       \r\n  \"rating\": 4.5,                        \r\n  \"tags\": [\"news\", \"media\"]                \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/media-channels/create", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "media-channels", "create"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "605"}, {"key": "ETag", "value": "W/\"25d-tCxt2/g26boRM31EvB4GmBBPrRg\""}, {"key": "Date", "value": "Fri, 25 Jul 2025 21:22:54 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"is_deleted\": false,\n        \"id\": 4,\n        \"name\": \"Example Channel 4\",\n        \"type\": \"online\",\n        \"category\": \"News\",\n        \"website\": \"https://example.com\",\n        \"description\": \"A sample media channel.\",\n        \"reach\": \"100000\",\n        \"audience_demographics\": {\n            \"age\": \"18-35\",\n            \"gender\": \"mixed\"\n        },\n        \"contact_info\": {\n            \"email\": \"<EMAIL>\",\n            \"phone\": \"+123456789\"\n        },\n        \"social_handles\": {\n            \"twitter\": \"@example\"\n        },\n        \"location\": \"Lagos\",\n        \"language\": \"English\",\n        \"frequency\": \"Daily\",\n        \"status\": \"active\",\n        \"rating\": \"4.5\",\n        \"tags\": [\n            \"news\",\n            \"media\"\n        ],\n        \"updatedAt\": \"2025-07-25T21:22:54.645Z\",\n        \"createdAt\": \"2025-07-25T21:22:54.645Z\"\n    },\n    \"message\": \"Media channel created successfully\"\n}"}]}]}, {"name": "Nature", "item": [{"name": "GetAll", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsImlhdCI6MTc1MzQ2MzgyNCwiZXhwIjoxNzUzNTUwMjI0fQ.v1jgi_Sh7ek45_4fm875YytxbbuzHxJTEvduKOv9-f4", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "files", "type": "file", "src": ["postman-cloud:///1f01bfc7-eaea-4a50-bcab-2539e4061865", "postman-cloud:///1f01bffe-2aaa-4510-ade0-90486651c007"]}, {"key": "description", "value": "New", "type": "text"}, {"key": "tags", "value": "{\"Project\":\"2025\"}", "type": "text"}]}, "url": {"raw": "http://localhost:3000/api/natures/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "natures", ""]}}, "response": [{"name": "getAll", "originalRequest": {"method": "GET", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "files", "type": "file", "src": ["postman-cloud:///1f01bfc7-eaea-4a50-bcab-2539e4061865", "postman-cloud:///1f01bffe-2aaa-4510-ade0-90486651c007"]}, {"key": "description", "value": "New", "type": "text"}, {"key": "tags", "value": "{\"Project\":\"2025\"}", "type": "text"}]}, "url": {"raw": "http://localhost:3000/api/natures/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "natures", ""]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "362"}, {"key": "ETag", "value": "W/\"16a-tbou27DfhfuQPaHqkkqw4frtyXk\""}, {"key": "Date", "value": "Sat, 26 Jul 2025 06:02:25 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"nature\": [\n            {\n                \"id\": 1,\n                \"name\": \"Yes\"\n            },\n            {\n                \"id\": 2,\n                \"name\": \"No\"\n            }\n        ],\n        \"meta\": {\n            \"total\": 2,\n            \"currentPage\": 1,\n            \"totalPage\": 1,\n            \"pageSize\": 10\n        },\n        \"links\": {\n            \"first\": \"http://localhost:3000/natures?page=1\",\n            \"last\": \"http://localhost:3000/natures?page=1\",\n            \"prev\": \"http://localhost:3000/natures?page=1\",\n            \"next\": \"http://localhost:3000/natures?pages=1\"\n        }\n    },\n    \"message\": \"All Natures\"\n}"}]}, {"name": "create", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\" : \"No\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/natures/create", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "natures", "create"]}}, "response": [{"name": "create", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\" : \"No\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/natures/create", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "natures", "create"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "53"}, {"key": "ETag", "value": "W/\"35-nOeFUKsE+N1xHY8hEDDldDIWUig\""}, {"key": "Date", "value": "Sat, 26 Jul 2025 06:02:13 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {},\n    \"message\": \"Nature Created\"\n}"}]}]}, {"name": "OutcomeInsights", "item": [{"name": "GetAll", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/outcome-insights/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "outcome-insights", ""]}}, "response": [{"name": "getAll", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/outcome-insights/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "outcome-insights", ""]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "1804"}, {"key": "ETag", "value": "W/\"70c-OKzLYjQAuHLjfmQVYKCu9O1pL3w\""}, {"key": "Date", "value": "Sat, 26 Jul 2025 06:27:56 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"data\": [\n            {\n                \"id\": 1,\n                \"company_id\": 1,\n                \"title\": \"Q2 2025 Performance Review\",\n                \"date\": \"2025-07-25T00:00:00.000Z\",\n                \"social_media_engagement\": {\n                    \"percentage\": 75,\n                    \"description\": \"Significant growth on Instagram\"\n                },\n                \"media_coverage\": {\n                    \"percentage\": 60,\n                    \"description\": \"Good coverage in national newspapers\"\n                },\n                \"competitor_comparison\": {\n                    \"percentage\": 50,\n                    \"description\": \"Competitors increased their ad spend\"\n                },\n                \"recommendations\": [\n                    \"Increase social media budget\",\n                    \"Engage influencers\"\n                ],\n                \"insights\": \"Overall positive outcome, but more can be done on Twitter.\",\n                \"key_metrics\": {\n                    \"reach\": 100000,\n                    \"engagement\": 5000\n                },\n                \"analyst_id\": 1,\n                \"status\": \"draft\",\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-26T06:23:15.904Z\",\n                \"updatedAt\": \"2025-07-26T06:23:15.904Z\",\n                \"company\": {\n                    \"id\": 1,\n                    \"name\": \"Dangote Group\",\n                    \"industry\": \"Conglomerate\"\n                },\n                \"analyst\": {\n                    \"id\": 1,\n                    \"username\": \"admin\",\n                    \"email\": \"<EMAIL>\"\n                }\n            },\n            {\n                \"id\": 2,\n                \"company_id\": 1,\n                \"title\": \"Q2 2025 Performance Review\",\n                \"date\": \"2025-07-25T00:00:00.000Z\",\n                \"social_media_engagement\": {\n                    \"percentage\": 75,\n                    \"description\": \"Significant growth on Instagram\"\n                },\n                \"media_coverage\": {\n                    \"percentage\": 60,\n                    \"description\": \"Good coverage in national newspapers\"\n                },\n                \"competitor_comparison\": {\n                    \"percentage\": 50,\n                    \"description\": \"Competitors increased their ad spend\"\n                },\n                \"recommendations\": [\n                    \"Increase social media budget\",\n                    \"Engage influencers\"\n                ],\n                \"insights\": \"Overall positive outcome, but more can be done on Twitter.\",\n                \"key_metrics\": {\n                    \"reach\": 100000,\n                    \"engagement\": 5000\n                },\n                \"analyst_id\": 1,\n                \"status\": \"draft\",\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-26T06:25:51.151Z\",\n                \"updatedAt\": \"2025-07-26T06:25:51.151Z\",\n                \"company\": {\n                    \"id\": 1,\n                    \"name\": \"Dangote Group\",\n                    \"industry\": \"Conglomerate\"\n                },\n                \"analyst\": {\n                    \"id\": 1,\n                    \"username\": \"admin\",\n                    \"email\": \"<EMAIL>\"\n                }\n            }\n        ],\n        \"pagination\": {\n            \"total\": 2,\n            \"page\": 1,\n            \"limit\": 10,\n            \"totalPages\": 1\n        }\n    },\n    \"message\": \"Outcome insights retrieved successfully\"\n}"}]}, {"name": "create", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsImlhdCI6MTc1MzUxMDkyOCwiZXhwIjoxNzUzNTk3MzI4fQ.NM6J5Y6pPpHXyM-l9WpvL5HrfWjLGJ47kZ8HXP4tDuE", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"company_id\": 1, \r\n  \"title\": \"Q2 2025 Performance Review\", \r\n  \"date\": \"2025-07-25T00:00:00Z\", \r\n  \"social_media_engagement\": {\r\n    \"percentage\": 75,\r\n    \"description\": \"Significant growth on Instagram\"\r\n  },\r\n  \"media_coverage\": {\r\n    \"percentage\": 60,\r\n    \"description\": \"Good coverage in national newspapers\"\r\n  },\r\n  \"competitor_comparison\": {\r\n    \"percentage\": 50,\r\n    \"description\": \"Competitors increased their ad spend\"\r\n  },\r\n  \"recommendations\": [\r\n    \"Increase social media budget\",\r\n    \"Engage influencers\"\r\n  ],\r\n  \"insights\": \"Overall positive outcome, but more can be done on Twitter.\",\r\n  \"key_metrics\": {\r\n    \"reach\": 100000,\r\n    \"engagement\": 5000\r\n  },\r\n  \"status\": \"draft\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/outcome-insights/create", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "outcome-insights", "create"]}}, "response": [{"name": "create", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"company_id\": 1, \r\n  \"title\": \"Q2 2025 Performance Review\", \r\n  \"date\": \"2025-07-25T00:00:00Z\", \r\n  \"social_media_engagement\": {\r\n    \"percentage\": 75,\r\n    \"description\": \"Significant growth on Instagram\"\r\n  },\r\n  \"media_coverage\": {\r\n    \"percentage\": 60,\r\n    \"description\": \"Good coverage in national newspapers\"\r\n  },\r\n  \"competitor_comparison\": {\r\n    \"percentage\": 50,\r\n    \"description\": \"Competitors increased their ad spend\"\r\n  },\r\n  \"recommendations\": [\r\n    \"Increase social media budget\",\r\n    \"Engage influencers\"\r\n  ],\r\n  \"insights\": \"Overall positive outcome, but more can be done on Twitter.\",\r\n  \"key_metrics\": {\r\n    \"reach\": 100000,\r\n    \"engagement\": 5000\r\n  },\r\n  \"status\": \"draft\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/outcome-insights/create", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "outcome-insights", "create"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "901"}, {"key": "ETag", "value": "W/\"385-7KYvuPrlkV1rQqRrlxhpUJqSss4\""}, {"key": "Date", "value": "Sat, 26 Jul 2025 06:25:51 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"id\": 2,\n        \"company_id\": 1,\n        \"title\": \"Q2 2025 Performance Review\",\n        \"date\": \"2025-07-25T00:00:00.000Z\",\n        \"social_media_engagement\": {\n            \"percentage\": 75,\n            \"description\": \"Significant growth on Instagram\"\n        },\n        \"media_coverage\": {\n            \"percentage\": 60,\n            \"description\": \"Good coverage in national newspapers\"\n        },\n        \"competitor_comparison\": {\n            \"percentage\": 50,\n            \"description\": \"Competitors increased their ad spend\"\n        },\n        \"recommendations\": [\n            \"Increase social media budget\",\n            \"Engage influencers\"\n        ],\n        \"insights\": \"Overall positive outcome, but more can be done on Twitter.\",\n        \"key_metrics\": {\n            \"reach\": 100000,\n            \"engagement\": 5000\n        },\n        \"analyst_id\": 1,\n        \"status\": \"draft\",\n        \"is_deleted\": false,\n        \"createdAt\": \"2025-07-26T06:25:51.151Z\",\n        \"updatedAt\": \"2025-07-26T06:25:51.151Z\",\n        \"company\": {\n            \"id\": 1,\n            \"name\": \"Dangote Group\",\n            \"industry\": \"Conglomerate\"\n        },\n        \"analyst\": {\n            \"id\": 1,\n            \"username\": \"admin\",\n            \"email\": \"<EMAIL>\"\n        }\n    },\n    \"message\": \"Outcome insight created successfully\"\n}"}]}]}, {"name": "Permission", "item": [{"name": "create", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"no\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/permissions/create", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "permissions", "create"]}}, "response": [{"name": "create", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"no\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/permissions/create", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "permissions", "create"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "57"}, {"key": "ETag", "value": "W/\"39-zshPZWso6WdC4FS3Z+7v+TUOWas\""}, {"key": "Date", "value": "Sat, 26 Jul 2025 06:40:12 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {},\n    \"message\": \"Permission Created\"\n}"}]}, {"name": "getAll", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"no\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/permissions/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "permissions", ""]}}, "response": [{"name": "getAll", "originalRequest": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"no\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/permissions/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "permissions", ""]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "379"}, {"key": "ETag", "value": "W/\"17b-Ndu9vpcdU95N6Y8PIfjDLakAeSQ\""}, {"key": "Date", "value": "Sat, 26 Jul 2025 06:41:30 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"data\": [\n            {\n                \"id\": 1,\n                \"name\": \"yes\"\n            },\n            {\n                \"id\": 2,\n                \"name\": \"no\"\n            }\n        ],\n        \"meta\": {\n            \"total\": 2,\n            \"currentPage\": 1,\n            \"totalPage\": 1,\n            \"pageSize\": 10\n        },\n        \"links\": {\n            \"first\": \"http://localhost:3000/permissions?page=1\",\n            \"last\": \"http://localhost:3000/permissions?page=1\",\n            \"prev\": \"http://localhost:3000/permissions?page=1\",\n            \"next\": \"http://localhost:3000/permissions?pages=1\"\n        }\n    },\n    \"message\": \"All Permission\"\n}"}]}]}, {"name": "Publication", "item": [{"name": "create", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"yes\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/publications/create", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "publications", "create"]}}, "response": [{"name": "create", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"yes\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/publications/create", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "publications", "create"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "58"}, {"key": "ETag", "value": "W/\"3a-lTy80oLvklQtKQaQopzkJqUIlp8\""}, {"key": "Date", "value": "Sat, 26 Jul 2025 09:31:43 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {},\n    \"message\": \"Publication Created\"\n}"}]}, {"name": "getAll", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"yes\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/publications/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "publications", ""]}}, "response": [{"name": "getAll", "originalRequest": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"yes\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/publications/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "publications", ""]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "624"}, {"key": "ETag", "value": "W/\"270-a0wx8+t/eNbUUj8VO+92ChjjCPI\""}, {"key": "Date", "value": "Sat, 26 Jul 2025 09:33:04 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"publication\": [\n            {\n                \"id\": 1,\n                \"name\": \"The Punch\"\n            },\n            {\n                \"id\": 2,\n                \"name\": \"Vanguard\"\n            },\n            {\n                \"id\": 3,\n                \"name\": \"ThisDay\"\n            },\n            {\n                \"id\": 4,\n                \"name\": \"Channels TV\"\n            },\n            {\n                \"id\": 5,\n                \"name\": \"TechCabal\"\n            },\n            {\n                \"id\": 6,\n                \"name\": \"Nairametrics\"\n            },\n            {\n                \"id\": 7,\n                \"name\": \"Premium Times\"\n            },\n            {\n                \"id\": 8,\n                \"name\": \"The Cable\"\n            },\n            {\n                \"id\": 9,\n                \"name\": \"no\"\n            },\n            {\n                \"id\": 10,\n                \"name\": \"yes\"\n            }\n        ],\n        \"meta\": {\n            \"total\": 10,\n            \"currentPage\": 1,\n            \"totalPage\": 1,\n            \"pageSize\": 10\n        },\n        \"links\": {\n            \"first\": \"http://localhost:3000/publications?page=1\",\n            \"last\": \"http://localhost:3000/publications?page=1\",\n            \"prev\": \"http://localhost:3000/publications?page=1\",\n            \"next\": \"http://localhost:3000/publications?pages=1\"\n        }\n    },\n    \"message\": \"All Publications\"\n}"}]}]}, {"name": "SwotAnalysis", "item": [{"name": "getAll", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"yes\"\r\n}"}, "url": {"raw": "http://localhost:3000/api/swot-analysis/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "swot-analysis", ""]}}, "response": [{"name": "getAll", "originalRequest": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"yes\"\r\n}"}, "url": {"raw": "http://localhost:3000/api/swot-analysis/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "swot-analysis", ""]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "1454"}, {"key": "ETag", "value": "W/\"5ae-dlZYT1bfIhtNghMBsaZMWqnI7zY\""}, {"key": "Date", "value": "Sat, 26 Jul 2025 09:48:59 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"data\": [\n            {\n                \"id\": 1,\n                \"company_id\": 1,\n                \"title\": \"Q2 2025 SWOT Analysis\",\n                \"date\": \"2025-07-25T00:00:00.000Z\",\n                \"strengths\": [\n                    \"Strong brand\",\n                    \"Wide distribution\"\n                ],\n                \"weaknesses\": [\n                    \"Limited online presence\"\n                ],\n                \"opportunities\": [\n                    \"Emerging markets\"\n                ],\n                \"threats\": [\n                    \"New competitors\"\n                ],\n                \"analyst_note\": \"Initial draft for review\",\n                \"supervisor_note\": \"Looks good\",\n                \"analyst_id\": 1,\n                \"supervisor_id\": 2,\n                \"status\": \"draft\",\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-26T09:47:47.574Z\",\n                \"updatedAt\": \"2025-07-26T09:47:47.574Z\",\n                \"company\": {\n                    \"id\": 1,\n                    \"name\": \"Dangote Group\",\n                    \"industry\": \"Conglomerate\"\n                },\n                \"analyst\": {\n                    \"id\": 1,\n                    \"username\": \"admin\",\n                    \"email\": \"<EMAIL>\"\n                },\n                \"supervisor\": {\n                    \"id\": 2,\n                    \"username\": \"supervisor\",\n                    \"email\": \"<EMAIL>\"\n                }\n            },\n            {\n                \"id\": 2,\n                \"company_id\": 1,\n                \"title\": \"Q2 2025 SWOT Analysis\",\n                \"date\": \"2025-07-25T00:00:00.000Z\",\n                \"strengths\": [\n                    \"Strong brand\",\n                    \"Wide distribution\"\n                ],\n                \"weaknesses\": [\n                    \"Limited online presence\"\n                ],\n                \"opportunities\": [\n                    \"Emerging markets\"\n                ],\n                \"threats\": [\n                    \"New competitors\"\n                ],\n                \"analyst_note\": \"Initial draft for review\",\n                \"supervisor_note\": \"Looks good\",\n                \"analyst_id\": 1,\n                \"supervisor_id\": null,\n                \"status\": \"draft\",\n                \"is_deleted\": false,\n                \"createdAt\": \"2025-07-26T09:48:17.311Z\",\n                \"updatedAt\": \"2025-07-26T09:48:17.311Z\",\n                \"company\": {\n                    \"id\": 1,\n                    \"name\": \"Dangote Group\",\n                    \"industry\": \"Conglomerate\"\n                },\n                \"analyst\": {\n                    \"id\": 1,\n                    \"username\": \"admin\",\n                    \"email\": \"<EMAIL>\"\n                },\n                \"supervisor\": null\n            }\n        ],\n        \"pagination\": {\n            \"total\": 2,\n            \"page\": 1,\n            \"limit\": 10,\n            \"totalPages\": 1\n        }\n    },\n    \"message\": \"SWOT analyses retrieved successfully\"\n}"}]}, {"name": "create", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsImlhdCI6MTc1MzUxMDkyOCwiZXhwIjoxNzUzNTk3MzI4fQ.NM6J5Y6pPpHXyM-l9WpvL5HrfWjLGJ47kZ8HXP4tDuE", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"company_id\": 1,\r\n  \"title\": \"Q2 2025 SWOT Analysis\", \r\n  \"date\": \"2025-07-25T00:00:00Z\",\r\n  \"strengths\": [\"Strong brand\", \"Wide distribution\"], \r\n  \"weaknesses\": [\"Limited online presence\"], \r\n  \"opportunities\": [\"Emerging markets\"], \r\n  \"threats\": [\"New competitors\"],\r\n  \"analyst_note\": \"Initial draft for review\", \r\n  \"supervisor_note\": \"Looks good\", \r\n  \"supervisor_id\": 2,\r\n  \"status\": \"draft\" // optional: \"draft\", \"pending\", \"approved\", \"rejected\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/swot-analysis/create", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "swot-analysis", "create"]}}, "response": [{"name": "create", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"company_id\": 1,\r\n  \"title\": \"Q2 2025 SWOT Analysis\", \r\n  \"date\": \"2025-07-25T00:00:00Z\",\r\n  \"strengths\": [\"Strong brand\", \"Wide distribution\"], \r\n  \"weaknesses\": [\"Limited online presence\"], \r\n  \"opportunities\": [\"Emerging markets\"], \r\n  \"threats\": [\"New competitors\"],\r\n  \"analyst_note\": \"Initial draft for review\", \r\n  \"supervisor_note\": \"Looks good\", \r\n  \"supervisor_id\": 2,\r\n  \"status\": \"draft\" // optional: \"draft\", \"pending\", \"approved\", \"rejected\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/swot-analysis/create", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "swot-analysis", "create"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "677"}, {"key": "ETag", "value": "W/\"2a5-bqHIn6JO4eK8bJxni4N0AUnDkLc\""}, {"key": "Date", "value": "Sat, 26 Jul 2025 09:48:17 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"id\": 2,\n        \"company_id\": 1,\n        \"title\": \"Q2 2025 SWOT Analysis\",\n        \"date\": \"2025-07-25T00:00:00.000Z\",\n        \"strengths\": [\n            \"Strong brand\",\n            \"Wide distribution\"\n        ],\n        \"weaknesses\": [\n            \"Limited online presence\"\n        ],\n        \"opportunities\": [\n            \"Emerging markets\"\n        ],\n        \"threats\": [\n            \"New competitors\"\n        ],\n        \"analyst_note\": \"Initial draft for review\",\n        \"supervisor_note\": \"Looks good\",\n        \"analyst_id\": 1,\n        \"supervisor_id\": null,\n        \"status\": \"draft\",\n        \"is_deleted\": false,\n        \"createdAt\": \"2025-07-26T09:48:17.311Z\",\n        \"updatedAt\": \"2025-07-26T09:48:17.311Z\",\n        \"company\": {\n            \"id\": 1,\n            \"name\": \"Dangote Group\",\n            \"industry\": \"Conglomerate\"\n        },\n        \"analyst\": {\n            \"id\": 1,\n            \"username\": \"admin\",\n            \"email\": \"<EMAIL>\"\n        }\n    },\n    \"message\": \"SWOT analysis created successfully\"\n}"}]}]}, {"name": "Users", "item": [{"name": "getAll", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"yes\"\r\n}"}, "url": {"raw": "http://localhost:3000/api/users/4", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "4"]}}, "response": [{"name": "getAll", "originalRequest": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"yes\"\r\n}"}, "url": {"raw": "http://localhost:3000/api/users/4", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "4"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "196"}, {"key": "ETag", "value": "W/\"c4-PIF3g05FPANcpH6ScEbgBfYua8Y\""}, {"key": "Date", "value": "Sat, 26 Jul 2025 22:08:16 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"id\": 4,\n        \"username\": \"analyst1\",\n        \"email\": \"<EMAIL>\",\n        \"gender\": \"male\",\n        \"status\": \"enabled\",\n        \"role\": \"Admin\",\n        \"date\": \"2025-07-26T16:40:53.203Z\"\n    },\n    \"message\": \"Single User\"\n}"}]}, {"name": "getSupervisor", "request": {"method": "GET", "header": []}, "response": [{"name": "getSupervisor", "originalRequest": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"password123\"\r\n}"}, "url": {"raw": "http://localhost:3000/api/users/supervisors", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "supervisors"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "170"}, {"key": "ETag", "value": "W/\"aa-18nYBKUXJf0KnlACTC7n/8J2zhY\""}, {"key": "Date", "value": "Sat, 26 Jul 2025 21:52:10 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": [\n        {\n            \"id\": 3,\n            \"username\": \"supervisor\",\n            \"email\": \"<EMAIL>\",\n            \"role\": {\n                \"name\": \"Supervisor\"\n            }\n        }\n    ],\n    \"message\": \"Supervisors retrieved successfully\"\n}"}]}]}, {"name": "Role", "item": [{"name": "get", "request": {"method": "GET", "header": []}, "response": []}, {"name": "getUserRole", "request": {"method": "GET", "header": []}, "response": []}, {"name": "getUserRole Copy", "request": {"method": "GET", "header": []}, "response": []}]}]}